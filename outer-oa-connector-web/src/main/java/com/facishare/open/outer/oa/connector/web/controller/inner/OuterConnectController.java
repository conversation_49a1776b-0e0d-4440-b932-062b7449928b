package com.facishare.open.outer.oa.connector.web.controller.inner;

import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEnterpriseBindParams;
import com.facishare.open.outer.oa.connector.common.api.admin.BaseConnectorVo;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaAppInfoTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.outerInterface.OuterAbstractSettingService;
import com.facishare.open.outer.oa.connector.common.api.result.Result;
import com.facishare.open.outer.oa.connector.common.api.result.ResultCodeEnum;
import com.facishare.open.outer.oa.connector.i18n.I18NStringManager;
import com.facishare.open.outer.oa.connector.web.manager.LicenseManager;
import com.facishare.open.outer.oa.connector.web.manager.OuterOASettingFactory;
import com.facishare.open.outer.oa.connector.web.manager.RedisManager;
import com.facishare.open.outer.oa.connector.web.result.InnerOAConnectResult;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 连接器内部管理Controller
 */
@Slf4j
@RestController
@RequestMapping("/inner/outerOa/connect")
public class OuterConnectController {

    @Autowired
    private LicenseManager licenseManager;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Autowired
    private OuterOASettingFactory outerOASettingFactory;
    @Autowired
    private RedissonClient  redissonClient;
    private static final String  KEY_PREFIX="outerOaConnect:%s";
    @Autowired
    private I18NStringManager i18NStringManager;

    /**
     * 提供给集成平台返回对应连接器列表
     *
     * @param tenantId
     * @return
     */
    @RequestMapping(value = "/getOrInitConnect", method = RequestMethod.POST)
    public Result<List<InnerOAConnectResult>> getOrInitConnect(@RequestParam(name = "tenantId") String  tenantId) {
        //需要校验有没有对应的license
        String fsEa = eieaConverter.enterpriseIdToAccount(Integer.valueOf(tenantId));
        List<InnerOAConnectResult> dataResult= Lists.newArrayList();
        String initKey=String.format(KEY_PREFIX, fsEa);
        RLock lock = redissonClient.getLock(initKey);
        try {
            if(lock.tryLock(10, 30, TimeUnit.SECONDS)){
                try {
                    for (ChannelEnum channelEnum : ChannelEnum.values()) {
                        try {
                            OuterAbstractSettingService implementation = outerOASettingFactory
                                    .getImplementation(channelEnum, OuterOaAppInfoTypeEnum.selfBuild);
                            Result<Boolean> routeNewPage = implementation.routeNewPage(tenantId, channelEnum);
                            if(routeNewPage!=null&&routeNewPage.isSuccess()&&routeNewPage.getData()){
                                log.info("channel enum route page:{}",tenantId);
                                Result<Void> enterpriseResult = licenseManager.checkLicense(fsEa,channelEnum);
                            }
                        } catch (Exception e) {
                            log.error("getOR init connect :{}",e);
                        }
                    }

                    OuterOaEnterpriseBindParams outerOaEnterpriseBindParams=OuterOaEnterpriseBindParams.builder().fsEa(fsEa).build();
                    List<OuterOaEnterpriseBindEntity> outerOaEnterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(outerOaEnterpriseBindParams);
                    for (OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity : outerOaEnterpriseBindEntities) {
                        InnerOAConnectResult innerOAConnectResult=new InnerOAConnectResult();
                        ChannelEnum channelEnum=outerOaEnterpriseBindEntity.getChannel();
                        innerOAConnectResult.setChannel(channelEnum.getErpChannelName());
                        if(ObjectUtils.isNotEmpty(outerOaEnterpriseBindEntity.getConnectInfo())){
                            BaseConnectorVo baseConnectorVo = JSONObject.parseObject(outerOaEnterpriseBindEntity.getConnectInfo(), outerOaEnterpriseBindEntity.getChannel().getClassName());
                            innerOAConnectResult.setConnectorName(baseConnectorVo.getDataCenterName());
                            if(outerOaEnterpriseBindEntity.getChannel().getI18nKey()!=null){
                                String connectVoName = i18NStringManager.getLangByEa(outerOaEnterpriseBindEntity.getChannel().getI18nKey().getI18nKey(), null, fsEa, baseConnectorVo.getConnectorName());
                                innerOAConnectResult.setConnectorName(connectVoName);
                            }
                        }
                        innerOAConnectResult.setHasConnect(true);
                        innerOAConnectResult.setId(outerOaEnterpriseBindEntity.getId());
                        innerOAConnectResult.setHasErpChannel(false);
                        innerOAConnectResult.setHasInited(true);
                        innerOAConnectResult.setConnectorType("OA");
                        dataResult.add(innerOAConnectResult);
                    }
                } finally {
                    if (lock.isHeldByCurrentThread()) {
                        lock.unlock();
                    }
                }
            } else {
                log.warn("Failed to acquire lock for key: {}", initKey);
                return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
            }
        } catch (Exception e) {
            log.error("Error in getOrInitConnect for tenantId: {}", tenantId, e);
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
        }

        return Result.newSuccess(dataResult);
    }

    /**
     * 增加enableAddConnect连接器
     *
     * @param tenantId
     * @return
     */
    @RequestMapping(value = "/enableAddConnect", method = RequestMethod.POST)
    public Result<InnerOAConnectResult> enableAddConnect(@RequestParam(name = "tenantId") String  tenantId,@RequestParam(name = "channelEnum") String  channelEnum) {
        //需要校验有没有对应的license
        log.info("enableAddConnect tenantId:{}",tenantId);
        String fsEa = eieaConverter.enterpriseIdToAccount(Integer.valueOf(tenantId));

        long statusCreate = outerOaEnterpriseBindManager.getStatusCreateEnterprise(fsEa, ChannelEnum.getByErpChannel(channelEnum));
        if(statusCreate>0){
            return Result.newError(ResultCodeEnum.OUTER_OA_HAS_MORE_CREATE);
        }
        //需要判断oa连接器是不是存在同种类型create状态的企业连接器
        OuterOaEnterpriseBindEntity enterpriseBindEntity = licenseManager.getOuterOaEnterpriseBindEntity(fsEa, tenantId, ChannelEnum.getByErpChannel(channelEnum));
        Integer unsertCount = outerOaEnterpriseBindManager.batchUpsert(Lists.newArrayList(enterpriseBindEntity));

        InnerOAConnectResult innerOAConnectResult=new InnerOAConnectResult();
        innerOAConnectResult.setChannel(enterpriseBindEntity.getChannel().getErpChannelName());
        if(ObjectUtils.isNotEmpty(enterpriseBindEntity.getConnectInfo())){
            BaseConnectorVo baseConnectorVo = JSONObject.parseObject(enterpriseBindEntity.getConnectInfo(), enterpriseBindEntity.getChannel().getClassName());
            innerOAConnectResult.setConnectorName(baseConnectorVo.getConnectorName());
        }
        innerOAConnectResult.setHasConnect(true);
        innerOAConnectResult.setId(enterpriseBindEntity.getId());
        innerOAConnectResult.setHasErpChannel(false);
        innerOAConnectResult.setHasInited(true);
        innerOAConnectResult.setConnectorType("OA");
        return Result.newSuccess(innerOAConnectResult);
    }



}
