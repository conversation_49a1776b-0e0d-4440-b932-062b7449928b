<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:mvc="http://www.springframework.org/schema/mvc"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
		http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context.xsd http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc.xsd">

    <context:component-scan base-package="com.facishare.open.outer.oa.connector.web.controller" />
    <context:component-scan base-package="com.facishare.open.outer.oa.connector.web.interceptor" />
    <mvc:annotation-driven/>
    
    <!-- 静态资源访问配置 -->
    <mvc:resources mapping="/static/**" location="/static/"/>
    
    <!-- 拦截器配置 -->
    <mvc:interceptors>
        <mvc:interceptor>
            <mvc:mapping path="/admin/**"/>
            <bean class="com.facishare.open.outer.oa.connector.web.interceptor.AdminInterceptor"/>
        </mvc:interceptor>
    </mvc:interceptors>
    
    <!-- 视图解析器配置 -->
    <bean class="org.springframework.web.servlet.view.InternalResourceViewResolver">
        <property name="prefix" value="/WEB-INF/views/"/>
        <property name="suffix" value=".jsp"/>
    </bean>
</beans>


