package com.facishare.open.oa.base.dbproxy.manager;

import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.oa.base.dbproxy.configVo.ConstantDb;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaConfigInfoEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaDepartmentBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaDeptDataEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEmployeeBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEmployeeDataEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.*;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaDepartmentBindParams;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaDeptDataParams;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEmployeeBindParams;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEmployeeDataParams;
import com.facishare.open.order.contacts.proxy.api.service.FsEmployeeServiceProxy;
import com.facishare.open.order.contacts.proxy.api.utils.ModifyNameUtils;
import com.facishare.open.outer.oa.connector.common.api.enums.*;
import com.facishare.open.outer.oa.connector.common.api.info.SettingAccountRulesModel;
import com.facishare.open.outer.oa.connector.common.api.result.Result;
import com.facishare.open.outer.oa.connector.common.api.result.ResultCodeEnum;
import com.facishare.open.outer.oa.connector.common.api.result.SettingsResult;
import com.facishare.open.outer.oa.connector.common.api.result.SystemFieldMappingResult;
import com.facishare.webhook.common.util.Constant;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.crmrestapi.arg.ActionAddArg;
import com.fxiaoke.crmrestapi.arg.IncrementUpdateArg;
import com.fxiaoke.crmrestapi.arg.v3.FindV3Arg;
import com.fxiaoke.crmrestapi.arg.v3.GetByIdArg;
import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.fxiaoke.crmrestapi.common.contants.FilterOperatorEnum;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.SearchTemplateQuery;
import com.fxiaoke.crmrestapi.result.ActionAddResult;
import com.fxiaoke.crmrestapi.result.IncrementUpdateResult;
import com.fxiaoke.crmrestapi.result.v3.ObjectDataGetByIdV3Result;
import com.fxiaoke.crmrestapi.result.v3.ObjectDataQueryListResult;
import com.fxiaoke.crmrestapi.service.MetadataActionService;
import com.fxiaoke.crmrestapi.service.ObjectDataServiceV3;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ObjectDataManager {
    @Autowired
    private OuterOaEmployeeDataManager outerOaEmployeeDataManager;
    @Autowired
    private OuterOaDeptDataManager outerOaDeptDataManager;
    @Autowired
    private DescManager descManager;
    @Autowired
    private ObjectDataServiceV3 objectDataServiceV3;
    @Autowired
    private MetadataActionService metadataActionService;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private OuterOaEmployeeBindManager outerOaEmployeeBindManager;
    @Autowired
    private OuterOaDepartmentBindManager outerOaDepartmentBindManager;
    @Autowired
    private OuterOaConfigInfoManager configInfoManager;
    @Autowired
    private OuterOaConfigInfoManager outerOaConfigInfoManager;
    @Autowired
    private FsEmployeeServiceProxy fsEmployeeServiceProxy;
    @Autowired
    private FieldTypeConvertManager fieldTypeConvertManager;


    private final String userNamePrefix = "U-FSQYWX-";
    private final Set<String> convertEmployeeFields = Sets.newHashSet("name", "full_name");
    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;

    /**
     * 新增人员
     *
     * @param outerOaEnterpriseBindEntity
     * @param outerUserId
     * @return
     */
    public Result<ActionAddResult> createEmployee(OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity,
            String outerUserId) {
        try {
            if(hasManualAccount(outerOaEnterpriseBindEntity)){
                return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
            }
            Result<ObjectData> objectDataResult = convertObjectData(outerOaEnterpriseBindEntity, outerUserId, true);
            if (!objectDataResult.isSuccess()) {
                return Result.newError(objectDataResult.getCode(), objectDataResult.getMsg());
            }
            // 设置租户ID
            Integer tenantId = eieaConverter.enterpriseAccountToId(outerOaEnterpriseBindEntity.getFsEa());
            HeaderObj headerObj = HeaderObj.newInstance(tenantId, ConstantDb.CRM_OPERATOR_USER_ID);
            ActionAddArg actionAddArg = new ActionAddArg();
            actionAddArg.setObjectData(objectDataResult.getData());
            com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> actionAddResultResult = metadataActionService
                    .add(headerObj, AccountTypeEnum.EMP_BIND.getCode(), false, false, false, true, actionAddArg);
            log.info("create employee crmArg:{},result:{}", JSONObject.toJSONString(actionAddArg), actionAddResultResult);
            if (actionAddResultResult.isSuccess()) {
                // 插入中间表
                OuterOaEmployeeBindEntity outerOaEmployeeBindEntity = OuterOaEmployeeBindEntity.builder()
                        .id(IdGenerator.get())
                        .dcId(outerOaEnterpriseBindEntity.getId())
                        .channel(outerOaEnterpriseBindEntity.getChannel()).appId(outerOaEnterpriseBindEntity.getAppId())
                        .outEa(outerOaEnterpriseBindEntity.getOutEa()).fsEa(outerOaEnterpriseBindEntity.getFsEa())
                        .outEmpId(outerUserId).fsEmpId(actionAddResultResult.getData().getObjectData().getId())
                        .createTime(System.currentTimeMillis()).updateTime(System.currentTimeMillis())
                        .bindStatus(BindStatusEnum.normal).build();
                outerOaEmployeeBindManager.batchUpsert(Lists.newArrayList(outerOaEmployeeBindEntity));
                return Result.newSuccess(actionAddResultResult.getData());
            } else {
                return Result.newError(actionAddResultResult.getCode(), actionAddResultResult.getMessage());
            }

        } catch (Exception e) {
            log.error("create crm employee error", e);
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
        }
    }

    /**
     * 新增员工——outeaEmployeeData
     */
    /**
     * 新增人员（直接通过OuterOaEmployeeDataEntity对象）
     *
     * @param outerOaEnterpriseBindEntity 企业绑定实体
     * @param employeeDataEntity 外部OA人员数据实体
     * @return 创建结果
     */
    public Result<ActionAddResult> createEmployee(OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity,
                                                  OuterOaEmployeeDataEntity employeeDataEntity) {
        try {
            if (employeeDataEntity == null || StringUtils.isEmpty(employeeDataEntity.getOutUserId())) {
                log.warn("Employee data entity is null or outUserId is empty");
                return Result.newError(ResultCodeEnum.PARAMETER_ERROR);
            }
            if(hasManualAccount(outerOaEnterpriseBindEntity)){
                return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
            }

            Result<ObjectData> objectDataResult = convertObjectData(outerOaEnterpriseBindEntity, employeeDataEntity, true);
            if (!objectDataResult.isSuccess()) {
                return Result.newError(objectDataResult.getCode(), objectDataResult.getMsg());
            }

            // 设置租户ID
            Integer tenantId = eieaConverter.enterpriseAccountToId(outerOaEnterpriseBindEntity.getFsEa());
            HeaderObj headerObj = HeaderObj.newInstance(tenantId, ConstantDb.CRM_OPERATOR_USER_ID);
            ActionAddArg actionAddArg = new ActionAddArg();
            actionAddArg.setObjectData(objectDataResult.getData());
            com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> actionAddResultResult = metadataActionService
                    .add(headerObj, AccountTypeEnum.EMP_BIND.getCode(), false, false, false, true, actionAddArg);
            log.info("create employee with entity crmArg:{},result:{}", JSONObject.toJSONString(actionAddArg), actionAddResultResult);
            if (actionAddResultResult.isSuccess()) {
                // 插入中间表
                OuterOaEmployeeBindEntity outerOaEmployeeBindEntity = OuterOaEmployeeBindEntity.builder()
                        .id(IdGenerator.get())
                        .dcId(outerOaEnterpriseBindEntity.getId())
                        .channel(outerOaEnterpriseBindEntity.getChannel()).appId(outerOaEnterpriseBindEntity.getAppId())
                        .outEa(outerOaEnterpriseBindEntity.getOutEa()).fsEa(outerOaEnterpriseBindEntity.getFsEa())
                        .outEmpId(employeeDataEntity.getOutUserId()).fsEmpId(actionAddResultResult.getData().getObjectData().getId())
                        .bindStatus(BindStatusEnum.normal).build();
                outerOaEmployeeBindManager.batchUpsert(Lists.newArrayList(outerOaEmployeeBindEntity));
                return Result.newSuccess(actionAddResultResult.getData());
            } else {
                return Result.newError(actionAddResultResult.getCode(), actionAddResultResult.getMessage());
            }
        } catch (Exception e) {
            log.error("create crm employee with entity error", e);
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
        }
    }

    /**
     * 更新人员
     *
     * @param outerOaEnterpriseBindEntity
     * @param outerUserId
     * @return
     */
    public Result<IncrementUpdateResult> updateEmpData(OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity,
            String outerUserId) {
        try {
            Result<ObjectData> objectDataResult = convertObjectData(outerOaEnterpriseBindEntity, outerUserId, false);
            if (!objectDataResult.isSuccess()) {
                return Result.newError(objectDataResult.getCode(), objectDataResult.getMsg());
            }
            if(hasManualAccount(outerOaEnterpriseBindEntity)){
                return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
            }
            // 设置租户ID
            Integer tenantId = eieaConverter.enterpriseAccountToId(outerOaEnterpriseBindEntity.getFsEa());
            HeaderObj headerObj = HeaderObj.newInstance(tenantId, ConstantDb.CRM_OPERATOR_USER_ID);
            // TODO 更新数据
            OuterOaEmployeeBindParams outerOaEmployeeBindParams = OuterOaEmployeeBindParams.builder()
                    .channel(outerOaEnterpriseBindEntity.getChannel())
                    .outEa(outerOaEnterpriseBindEntity.getOutEa()).fsEa(outerOaEnterpriseBindEntity.getFsEa())
                    .outEmpId(outerUserId).build();
            List<OuterOaEmployeeBindEntity> bindEntities = outerOaEmployeeBindManager
                    .getEntities(outerOaEmployeeBindParams);
            if (CollectionUtils.isEmpty(bindEntities)) {
                return Result.newError(ResultCodeEnum.EMPLOYEE_NO_BIND_INFO);
            }
            //看看是否有appId的
            List<OuterOaEmployeeBindEntity> outerOaEmployeeBindEntities = bindEntities.stream().filter(v -> v.getAppId().equals(outerOaEnterpriseBindEntity.getAppId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(outerOaEmployeeBindEntities)) {
                //绑定上
                OuterOaEmployeeBindEntity outerOaEmployeeBindEntity = OuterOaEmployeeBindEntity.builder()
                        .id(IdGenerator.get())
                        .channel(outerOaEnterpriseBindEntity.getChannel()).appId(outerOaEnterpriseBindEntity.getAppId())
                        .outEa(outerOaEnterpriseBindEntity.getOutEa()).fsEa(outerOaEnterpriseBindEntity.getFsEa())
                        .outEmpId(outerUserId).fsEmpId(bindEntities.get(0).getFsEmpId())
                        .bindStatus(BindStatusEnum.normal).createTime(System.currentTimeMillis()).updateTime(System.currentTimeMillis())
                        .dcId(outerOaEnterpriseBindEntity.getId())
                        .build();
                Integer count = outerOaEmployeeBindManager.insert(outerOaEmployeeBindEntity);
                log.info("updateEmpData,insert employee bind entity,count={}", count);
                outerOaEmployeeBindEntities = Lists.newArrayList(outerOaEmployeeBindEntity);
            }

            IncrementUpdateArg incrementUpdateArg = new IncrementUpdateArg();
            ObjectData objectData = objectDataResult.getData();
            objectData.put("_id", bindEntities.get(0).getFsEmpId());
            incrementUpdateArg.setData(objectData);
            com.fxiaoke.crmrestapi.common.result.Result<IncrementUpdateResult> incrementUpdateResultResult = metadataActionService
                    .incrementUpdate(headerObj, AccountTypeEnum.EMP_BIND.getCode(), true, incrementUpdateArg);
            log.info("update employee data success tenantId:{}:empid:{}", tenantId, bindEntities.get(0).getFsEmpId());
            if (!incrementUpdateResultResult.isSuccess()) {
                if (incrementUpdateResultResult.getCode()==*********) {
                    log.error("update employee data error tenantId:{}:empid:{}", tenantId, bindEntities.get(0).getFsEmpId());
                    //兼容历史客户fsEmpId为字符串的场景，需要把数字ID转换成真实的基于对象的_id字段
                    com.facishare.open.order.contacts.proxy.api.result.Result<ObjectData> detail = fsEmployeeServiceProxy.detail(tenantId + "",
                            bindEntities.get(0).getFsEmpId());
                    if(detail.isSuccess()) {
                        objectData.put("_id", detail.getData().getId());
                        log.info("update employee data success tenantId:{}:empid:{}", tenantId, detail.getData().getId());
                        incrementUpdateResultResult = metadataActionService
                                .incrementUpdate(headerObj, AccountTypeEnum.EMP_BIND.getCode(), true, incrementUpdateArg);
                    }
                } else {
                    return Result.newError(incrementUpdateResultResult.getCode(), incrementUpdateResultResult.getMessage());
                }
            }
            if (incrementUpdateResultResult.isSuccess()) {
                OuterOaEmployeeBindEntity outerOaEmployeeBindEntity = outerOaEmployeeBindEntities.get(0);
                outerOaEmployeeBindEntity.setBindStatus(BindStatusEnum.normal);
                outerOaEmployeeBindEntity.setUpdateTime(System.currentTimeMillis());
                Integer count = outerOaEmployeeBindManager.updateById(outerOaEmployeeBindEntity);
                log.info("updateEmpData,update employee bind entity,count={}", count);
                return Result.newSuccess(incrementUpdateResultResult.getData());
            }
            return Result.newSuccess(incrementUpdateResultResult.getData());
        } catch (Exception e) {
            log.error("create crm employee error", e);
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
        }
    }

    /**
     * 删除人员，匹配规则
     *
     * @param outerOaEnterpriseBindEntity
     * @param outerUserId
     * @return
     */
    public Result<Void> removeEmpData(OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity, String outerUserId,
            RemoveEmployeeEventType removeEmployeeEventType) {
        //直接删除外部数据
        Integer count = outerOaEmployeeDataManager.deleteByUserId(outerOaEnterpriseBindEntity.getChannel(),
                outerOaEnterpriseBindEntity.getOutEa(),
                outerOaEnterpriseBindEntity.getAppId(),
                outerUserId);
        log.info("removeEmpData,unbind employee bind entity,count={}", count);
        OuterOaEmployeeBindParams outerOaEmployeeBindParams = OuterOaEmployeeBindParams.builder()
                .channel(outerOaEnterpriseBindEntity.getChannel()).appId(outerOaEnterpriseBindEntity.getAppId())
                .outEa(outerOaEnterpriseBindEntity.getOutEa()).fsEa(outerOaEnterpriseBindEntity.getFsEa())
                .bindStatus(BindStatusEnum.normal).outEmpId(outerUserId).build();
        List<OuterOaEmployeeBindEntity> bindEntities = outerOaEmployeeBindManager
                .getEntities(outerOaEmployeeBindParams);
        if (bindEntities.isEmpty()) {
            log.warn("remove empdata notfound data:{}", outerOaEmployeeBindParams);
            return Result.newError(ResultCodeEnum.EMPLOYEE_NO_BIND_INFO);
        }
       String fsEmpId= bindEntities.get(0).getFsEmpId();
        OuterOaConfigInfoEntity entityByAutoFieldDeFault = outerOaConfigInfoManager.getEntityByDataCenterId(
                OuterOaConfigInfoTypeEnum.SETTING_BIND_RULES, outerOaEnterpriseBindEntity.getId());
        // 解除绑定
        boolean unbindEmployee = false;
        // 停用员工
        boolean stopCrmEmployee = true;

        if (ObjectUtils.isNotEmpty(entityByAutoFieldDeFault)) {
            SettingAccountRulesModel settingAccountRulesModel = JSONObject
                    .parseObject(entityByAutoFieldDeFault.getConfigInfo(), SettingAccountRulesModel.class);
            if (removeEmployeeEventType == RemoveEmployeeEventType.RESIGN_EMPLOYEE) {
                unbindEmployee = settingAccountRulesModel.getEmployeeLeaveRule().getUnbind();
                stopCrmEmployee = settingAccountRulesModel.getEmployeeLeaveRule().getStopEmp();
            } else {
                unbindEmployee = settingAccountRulesModel.getEmployeeRangeRemoveRule().getUnbind();
                stopCrmEmployee = settingAccountRulesModel.getEmployeeRangeRemoveRule().getStopEmp();
            }
        } else {
            // 插入默认值
            SettingAccountRulesModel settingAccountRulesModel = new SettingAccountRulesModel(
                    EnterpriseConfigAccountSyncTypeEnum.accountBind, BindTypeEnum.manual,
                    new SettingAccountRulesModel.EmployeeRangeRemoveRule(true, false),
                    new SettingAccountRulesModel.EmployeeLeaveRule(true, true));
            OuterOaConfigInfoEntity outerOaConfigInfoEntity = OuterOaConfigInfoEntity.builder()
                    .channel(outerOaEnterpriseBindEntity.getChannel()).dcId(outerOaEnterpriseBindEntity.getId())
                    .fsEa(outerOaEnterpriseBindEntity.getFsEa()).outEa(outerOaEnterpriseBindEntity.getOutEa())
                    .type(OuterOaConfigInfoTypeEnum.SETTING_BIND_RULES)
                    .configInfo(JSONObject.toJSONString(settingAccountRulesModel))
                    .appId(outerOaEnterpriseBindEntity.getAppId()).build();
            outerOaConfigInfoManager.insert(outerOaConfigInfoEntity);
        }
        if (unbindEmployee) {
            OuterOaEmployeeBindEntity outerOaEmployeeBindEntity = bindEntities.get(0);
            outerOaEmployeeBindEntity.setBindStatus(BindStatusEnum.stop);
            Integer unbindCount = outerOaEmployeeBindManager.updateById(outerOaEmployeeBindEntity);
            log.info("removeEmpData,unbind employee bind entity,unbindCount={}", unbindCount);


        }
        //上面已经解绑，需要看下还有没有其他应用正常人员绑定
        List<OuterOaEmployeeBindEntity> normalStatusEmployees = outerOaEmployeeBindManager.getNormalStatusEmployees(null, null, outerOaEnterpriseBindEntity.getFsEa(),
                null, fsEmpId);
        stopCrmEmployee=(CollectionUtils.isNotEmpty(normalStatusEmployees)?false:true) && stopCrmEmployee;
        log.info("remove employee setting fsea:{},fsuserid:{}:stopCrmEmployee:{}:unbindEmployee{}", outerOaEnterpriseBindEntity.getFsEa(), fsEmpId,stopCrmEmployee,unbindEmployee);
        if (stopCrmEmployee) {
//            Result<ObjectData> objectDataResult = convertObjectData(outerOaEnterpriseBindEntity, outerUserId);
//            if (!objectDataResult.isSuccess()) {
//                return Result.newError(objectDataResult.getCode(), objectDataResult.getMsg());
//            }
            // 设置租户ID
            Integer tenantId = eieaConverter.enterpriseAccountToId(outerOaEnterpriseBindEntity.getFsEa());
            HeaderObj headerObj = HeaderObj.newInstance(tenantId, ConstantDb.CRM_OPERATOR_USER_ID);
            // TODO 更新数据
            IncrementUpdateArg incrementUpdateArg = new IncrementUpdateArg();
            ObjectData objectData = new ObjectData();
            List<ObjectData> crmEmployeeByUserId = getCRmEmployeeByUserId(tenantId, AccountTypeEnum.EMP_BIND.getCode(), Lists.newArrayList(fsEmpId));
            if(CollectionUtils.isNotEmpty(crmEmployeeByUserId)){
                String dataEmployeeId= crmEmployeeByUserId.get(0).getId().toString();
                objectData.put("_id", dataEmployeeId);
            }else{
                objectData.put("_id", fsEmpId);
            }
            objectData.put("status", "1");// 停用员工
            incrementUpdateArg.setData(objectData);
            com.fxiaoke.crmrestapi.common.result.Result<IncrementUpdateResult> incrementUpdateResultResult = metadataActionService
                    .incrementUpdate(headerObj, AccountTypeEnum.EMP_BIND.getCode(), true, incrementUpdateArg);
            log.info("stop employee data success tenantId:{}:empid:{}:incrementUpdateResultResult:{}", tenantId, bindEntities.get(0).getFsEmpId(), incrementUpdateResultResult);
        }
        return Result.newError(ResultCodeEnum.SUCCESS);

    }

    /**
     * 将OuterOaEmployeeDataEntity转换为ObjectData
     *
     * @param outerOaEnterpriseBindEntity 企业绑定实体
     * @param employeeDataEntity 外部OA人员数据实体
     * @return 转换结果
     */
    private Result<ObjectData> convertObjectData(OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity,
                                                 OuterOaEmployeeDataEntity employeeDataEntity, Boolean isCreate) {
        if (employeeDataEntity == null || employeeDataEntity.getOutUserInfo() == null) {
            log.warn("Employee data entity is null or outUserInfo is null");
            return Result.newError(ResultCodeEnum.OUTER_EMPDATA_NOT_EXISTS);
        }

        Result<SystemFieldMappingResult> orInitFieldMapping = descManager.getOrInitFieldMapping(
                outerOaEnterpriseBindEntity, OuterOaConfigInfoTypeEnum.EMPLOYEE_UNIQUE_IDENTITY);
        if (!orInitFieldMapping.isSuccess()) {
            log.warn("Get or init field mapping error:{}", orInitFieldMapping.getMsg());
            return Result.newError(ResultCodeEnum.PARAMETER_ERROR);
        }

        // 获取字段映射关系
        SystemFieldMappingResult fieldMappingResult = orInitFieldMapping.getData();
        List<SystemFieldMappingResult.ItemFieldMapping> itemFieldMappings = fieldMappingResult.getItemFieldMappings();
        if (CollectionUtils.isEmpty(itemFieldMappings)) {
            log.warn("Field mapping is empty, outUserId: {}", employeeDataEntity.getOutUserId());
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }

        // 创建CRM员工对象
        ObjectData objectData = new ObjectData();
        // 设置对象API名称为"PersonnelObj"(员工对象)
        objectData.put("object_describe_api_name", "PersonnelObj");

        // 根据字段映射关系，设置CRM员工对象的字段值
        JSONObject outUserInfo = employeeDataEntity.getOutUserInfo();
        //需要兼容附属部门
        if(employeeDataEntity.getOutDeptId()!=null){
            if(outUserInfo!=null){
                outUserInfo.put("vice_departments", employeeDataEntity.getOutDeptId());
            }
        }
        for (SystemFieldMappingResult.ItemFieldMapping mapping : itemFieldMappings) {
            String outerOAFieldApiName = mapping.getOuterOAFieldApiName();
            String crmFieldApiName = mapping.getCrmFieldApiName();

            if (outerOAFieldApiName != null && outUserInfo.containsKey(outerOAFieldApiName)) {
                Object fieldValue = outUserInfo.get(outerOAFieldApiName);
                // 不为空才传参
                if (fieldValue != null) {
                    //过滤特殊字段
                    if(filterSpecialField(objectData, crmFieldApiName, fieldValue, isCreate)) {
                        continue;
                    }
                    if(mapping.getFieldType()!=null){
                        Object convert = fieldTypeConvertManager.convert(outerOaEnterpriseBindEntity.getId(), fieldValue, mapping.getFieldType(), outerOaEnterpriseBindEntity.getFsEa());
                        fieldValue=convert;
                    }

                    //特殊字段转换
                    fieldValue = convertSpecialField(objectData, crmFieldApiName, fieldValue);

                    objectData.put(crmFieldApiName, fieldValue);
                }
            }
        }

        // 设置默认必填字段 (如状态、主属部门等)
        if (!objectData.containsKey(CRMEmployeeFiledEnum.STATUS.getCode())) {
            objectData.put(CRMEmployeeFiledEnum.STATUS.getCode(), "0"); // 设置默认状态为正常
        }
        //查询主属部门
        if(employeeDataEntity.getOutDeptId()!=null){
            OuterOaDepartmentBindEntity byOutDeptId = outerOaDepartmentBindManager.getByOutDeptId(outerOaEnterpriseBindEntity.getChannel(), outerOaEnterpriseBindEntity.getOutEa(), outerOaEnterpriseBindEntity.getFsEa(), outerOaEnterpriseBindEntity.getAppId(), employeeDataEntity.getOutDeptId());
            if(byOutDeptId!=null){
                objectData.put(CRMEmployeeFiledEnum.MAIN_DEPARTMENT.getCode(), Lists.newArrayList(byOutDeptId.getFsDepId()));
            }else{
                objectData.remove(CRMEmployeeFiledEnum.MAIN_DEPARTMENT.getCode());

            }

        }

        return Result.newSuccess(objectData);
    }

    private Boolean filterSpecialField(ObjectData objectData, String crmFieldApiName, Object fieldValue, Boolean isCreate) {
        boolean isFilter = false;
        if (!isCreate && CRMEmployeeFiledEnum.NAME.getCode().equals(crmFieldApiName)) {
            if (fieldValue.toString().startsWith(userNamePrefix)) {
                isFilter = true;
            }
        }
        return isFilter;
    }

    private Object convertSpecialField(ObjectData objectData, String crmFieldApiName, Object fieldValue) {
        //人员字段转换
        if (convertEmployeeFields.contains(crmFieldApiName)) {
            fieldValue = ModifyNameUtils.employeeValidName(String.valueOf(fieldValue));
        }
        return fieldValue;
    }

    /**
     * 将外部用户ID转换为ObjectData
     *
     * @param outerOaEnterpriseBindEntity 企业绑定实体
     * @param outerUserId 外部用户ID
     * @return 转换结果
     */
    private Result<ObjectData> convertObjectData(OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity,
                                                 String outerUserId, Boolean isCreate) {
        OuterOaEmployeeDataParams outerOaEmployeeDataParams = OuterOaEmployeeDataParams.builder()
                .channel(outerOaEnterpriseBindEntity.getChannel()).appId(outerOaEnterpriseBindEntity.getAppId())
                .outUserId(outerUserId).outEa(outerOaEnterpriseBindEntity.getOutEa()).build();
        List<OuterOaEmployeeDataEntity> entities = outerOaEmployeeDataManager.getEntities(outerOaEmployeeDataParams);
        if (CollectionUtils.isEmpty(entities)) {
            return Result.newError(ResultCodeEnum.OUTER_EMPDATA_NOT_EXISTS);
        }

        // 获取外部OA员工的数据
        OuterOaEmployeeDataEntity outerOaEmployeeData = entities.get(0);
        if (outerOaEmployeeData == null || outerOaEmployeeData.getOutUserInfo() == null) {
            log.warn("outer employee data not exists or outUserInfo is null, outUserId: {}", outerUserId);
            return Result.newError(ResultCodeEnum.OUTER_EMPDATA_NOT_EXISTS);
        }

        // 使用已有的处理OuterOaEmployeeDataEntity的方法继续处理
        return convertObjectData(outerOaEnterpriseBindEntity, outerOaEmployeeData, isCreate);
    }
    /**
     * 新增部门

     * @param outerOaEnterpriseBindEntity 企业绑定实体
     * @param outDeptId                   外部部门ID

     * @return 新增结果
     */
    public Result<ActionAddResult> createDepartment(OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity,
            String outDeptId) {
        try {
            if(!hasSyncAccount(outerOaEnterpriseBindEntity)){
                return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
            }
            Result<ObjectData> objectDataResult = convertDepartmentObjectData(outerOaEnterpriseBindEntity, outDeptId);
            if (!objectDataResult.isSuccess()) {
                return Result.newError(objectDataResult.getCode(), objectDataResult.getMsg());
            }
            // 设置租户ID
            Integer tenantId = eieaConverter.enterpriseAccountToId(outerOaEnterpriseBindEntity.getFsEa());
            HeaderObj headerObj = HeaderObj.newInstance(tenantId, ConstantDb.CRM_OPERATOR_USER_ID);
            ActionAddArg actionAddArg = new ActionAddArg();
            actionAddArg.setObjectData(objectDataResult.getData());
            com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> actionAddResultResult = metadataActionService
                    .add(headerObj, AccountTypeEnum.DEPT_BIND.getCode(), false, false, false, true, actionAddArg);

            if (actionAddResultResult.isSuccess()) {
                // 插入部门绑定表
                OuterOaDepartmentBindEntity outerOaDepartmentBindEntity = OuterOaDepartmentBindEntity.builder()
                        .id(IdGenerator.get())
                        .channel(outerOaEnterpriseBindEntity.getChannel()).appId(outerOaEnterpriseBindEntity.getAppId())
                        .outEa(outerOaEnterpriseBindEntity.getOutEa()).fsEa(outerOaEnterpriseBindEntity.getFsEa())
                        .outDepId(outDeptId).fsDepId(actionAddResultResult.getData().getObjectData().getId())
                        .bindStatus(BindStatusEnum.normal).dcId(outerOaEnterpriseBindEntity.getId()).build();
                outerOaDepartmentBindManager.batchUpsert(Lists.newArrayList(outerOaDepartmentBindEntity));
                log.info("Create department success: outDeptId={}, fsDeptId={}", outDeptId,
                        actionAddResultResult.getData().getObjectData().getId());
                return Result.newSuccess(actionAddResultResult.getData());
            } else {
                log.error("Create department failed: {}", actionAddResultResult.getMessage());
                return Result.newError(actionAddResultResult.getCode(), actionAddResultResult.getMessage());
            }
        } catch (Exception e) {
            log.error("Create CRM department error", e);
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
        }
    }

    /**
     * 更新部门
     *
     * @param outerOaEnterpriseBindEntity 企业绑定实体
     * @param outDeptId                   外部部门ID
     * @return 更新结果
     */
    public Result<IncrementUpdateResult> updateDepartment(OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity,
            String outDeptId) {
        try {
            if(!hasSyncAccount(outerOaEnterpriseBindEntity)){
                return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
            }
            Result<ObjectData> objectDataResult = convertDepartmentObjectData(outerOaEnterpriseBindEntity, outDeptId);
            if (!objectDataResult.isSuccess()) {
                return Result.newError(objectDataResult.getCode(), objectDataResult.getMsg());
            }

            // 查询部门绑定关系
            OuterOaDepartmentBindParams outerOaDepartmentBindParams = OuterOaDepartmentBindParams.builder()
                    .channel(outerOaEnterpriseBindEntity.getChannel()).appId(outerOaEnterpriseBindEntity.getAppId())
                    .outEa(outerOaEnterpriseBindEntity.getOutEa()).fsEa(outerOaEnterpriseBindEntity.getFsEa())
                    .outDepId(outDeptId).build();
            List<OuterOaDepartmentBindEntity> bindEntities = outerOaDepartmentBindManager
                    .getEntities(outerOaDepartmentBindParams);
            if (CollectionUtils.isEmpty(bindEntities)) {
                log.warn("Department binding not found: {}", outerOaDepartmentBindParams);
                return Result.newError(ResultCodeEnum.DEPARTMENT_NO_BIND_INFO);
            }

            // 设置租户ID
            Integer tenantId = eieaConverter.enterpriseAccountToId(outerOaEnterpriseBindEntity.getOutEa());
            HeaderObj headerObj = HeaderObj.newInstance(tenantId, ConstantDb.CRM_OPERATOR_USER_ID);

            // 增量更新部门数据
            IncrementUpdateArg incrementUpdateArg = new IncrementUpdateArg();
            ObjectData objectData = objectDataResult.getData();
            objectData.put("_id", bindEntities.get(0).getFsDepId());
            incrementUpdateArg.setData(objectData);

            com.fxiaoke.crmrestapi.common.result.Result<IncrementUpdateResult> updateResult = metadataActionService
                    .incrementUpdate(headerObj, AccountTypeEnum.DEPT_BIND.getCode(), true, incrementUpdateArg);

            if (updateResult.isSuccess()) {
                log.info("Update department success: outDeptId={}, fsDeptId={}", outDeptId,
                        bindEntities.get(0).getFsDepId());
                return Result.newSuccess(updateResult.getData());
            } else {
                log.error("Update department failed: {}", updateResult.getMessage());
                return Result.newError(updateResult.getCode(), updateResult.getMessage());
            }
        } catch (Exception e) {
            log.error("Update CRM department error", e);
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
        }
    }

    /**
     * 删除部门
     *
     * @param outerOaEnterpriseBindEntity 企业绑定实体
     * @param outDeptId                   外部部门ID
     * @return 删除结果
     */
    public Result<Void> removeDepartment(OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity, String outDeptId) {
        try {
            if(!hasSyncAccount(outerOaEnterpriseBindEntity)){
                return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
            }
            // 查询部门绑定关系
            OuterOaDepartmentBindParams outerOaDepartmentBindParams = OuterOaDepartmentBindParams.builder()
                    .channel(outerOaEnterpriseBindEntity.getChannel()).appId(outerOaEnterpriseBindEntity.getAppId())
                    .outEa(outerOaEnterpriseBindEntity.getOutEa()).fsEa(outerOaEnterpriseBindEntity.getFsEa())
                    .outDepId(outDeptId).build();
            List<OuterOaDepartmentBindEntity> bindEntities = outerOaDepartmentBindManager
                    .getEntities(outerOaDepartmentBindParams);
            if (CollectionUtils.isEmpty(bindEntities)) {
                log.warn("Department binding not found: {}", outerOaDepartmentBindParams);
                return Result.newError(ResultCodeEnum.DEPARTMENT_NO_BIND_INFO);
            }
            // 设置租户ID
            Integer tenantId = eieaConverter.enterpriseAccountToId(outerOaEnterpriseBindEntity.getOutEa());
            HeaderObj headerObj = HeaderObj.newInstance(tenantId, ConstantDb.CRM_OPERATOR_USER_ID);

            // 创建更新对象
            IncrementUpdateArg incrementUpdateArg = new IncrementUpdateArg();
            ObjectData objectData = new ObjectData();
            objectData.put("object_describe_api_name", AccountTypeEnum.DEPT_BIND.getCode());
            objectData.put("_id", bindEntities.get(0).getFsDepId());
            objectData.put(CRMDepartmentFieldEnum.STATUS.getCode(), "1"); // 设置部门状态为停用
            incrementUpdateArg.setData(objectData);

            com.fxiaoke.crmrestapi.common.result.Result<IncrementUpdateResult> updateResult = metadataActionService
                    .incrementUpdate(headerObj, AccountTypeEnum.DEPT_BIND.getCode(), true, incrementUpdateArg);

            if (updateResult.isSuccess()) {
                log.info("Disable department success: outDeptId={}, fsDeptId={}", outDeptId,
                        bindEntities.get(0).getFsDepId());
                return Result.newSuccess();
            } else {
                log.error("Disable department failed: {}", updateResult.getMessage());
                return Result.newError(updateResult.getCode(), updateResult.getMessage());
            }
        } catch (Exception e) {
            log.error("Remove CRM department error", e);
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
        }
    }


    /**
     * 删除部门
     *
     * @param outerOaEnterpriseBindEntity 企业绑定实体
     * @param crmDeptId                   crm部门id
     * @return 删除结果
     */
    public Result<Void> removeDepartmentByCrmDeptId(OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity, String crmDeptId) {
        try {
            if(!hasSyncAccount(outerOaEnterpriseBindEntity)){
                return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
            }
            // 查询部门绑定关系
            OuterOaDepartmentBindParams outerOaDepartmentBindParams = OuterOaDepartmentBindParams.builder()
                    .channel(outerOaEnterpriseBindEntity.getChannel()).fsEa(outerOaEnterpriseBindEntity.getFsEa())
                    .fsDepId(crmDeptId).build();
            List<OuterOaDepartmentBindEntity> bindEntities = outerOaDepartmentBindManager
                    .getEntities(outerOaDepartmentBindParams);
            if (CollectionUtils.isEmpty(bindEntities)) {
                log.warn("Department binding not found: {}", outerOaDepartmentBindParams);
                return Result.newError(ResultCodeEnum.DEPARTMENT_NO_BIND_INFO);
            }
            //需要判断是不是只有这个连接器使用这个纷享部门
            List<OuterOaDepartmentBindEntity> notExistsAppDeptBinds = bindEntities.stream().filter(item -> !item.getAppId().equals(outerOaEnterpriseBindEntity.getAppId())).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(notExistsAppDeptBinds)){
                log.info("crm dept has bind other app:{}:{}",outerOaEnterpriseBindEntity.getFsEa(),crmDeptId);
                return Result.newSuccess();
            }
            // 设置租户ID
            Integer tenantId = eieaConverter.enterpriseAccountToId(outerOaEnterpriseBindEntity.getFsEa());
            HeaderObj headerObj = HeaderObj.newInstance(tenantId, ConstantDb.CRM_OPERATOR_USER_ID);

            // 创建更新对象
            IncrementUpdateArg incrementUpdateArg = new IncrementUpdateArg();
            ObjectData objectData = new ObjectData();
            objectData.put("object_describe_api_name", AccountTypeEnum.DEPT_BIND.getCode());
            objectData.put("_id", bindEntities.get(0).getFsDepId());
            objectData.put(CRMDepartmentFieldEnum.STATUS.getCode(), "1"); // 设置部门状态为停用
            incrementUpdateArg.setData(objectData);

            com.fxiaoke.crmrestapi.common.result.Result<IncrementUpdateResult> updateResult = metadataActionService
                    .incrementUpdate(headerObj, AccountTypeEnum.DEPT_BIND.getCode(), false, incrementUpdateArg);

            if (updateResult.isSuccess()) {
                log.info("Disable department success: , fsDeptId={}",
                        bindEntities.get(0).getFsDepId());
                outerOaDepartmentBindManager.unbindDeptStatus(outerOaEnterpriseBindEntity.getId(), crmDeptId);
                return Result.newSuccess();
            } else {
                log.error("Disable department failed: {}", updateResult.getMessage());
                return Result.newError(updateResult.getCode(), updateResult.getMessage());
            }
        } catch (Exception e) {
            log.error("Remove CRM department error", e);
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
        }
    }

    /**
     * 转换为部门ObjectData
     *
     * @param outerOaEnterpriseBindEntity 企业绑定实体
     * @param outDeptId                   外部部门ID
     * @return 转换结果
     */
    private Result<ObjectData> convertDepartmentObjectData(OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity,
            String outDeptId) {
        // 查询外部部门数据
        OuterOaDeptDataParams outerOaDeptDataParams = OuterOaDeptDataParams.builder()
                .channel(outerOaEnterpriseBindEntity.getChannel()).appId(outerOaEnterpriseBindEntity.getAppId())
                .outDeptId(outDeptId).outEa(outerOaEnterpriseBindEntity.getOutEa()).build();
        List<OuterOaDeptDataEntity> entities = outerOaDeptDataManager.getEntities(outerOaDeptDataParams);
        if (CollectionUtils.isEmpty(entities)) {
            log.warn("Department data not found: {}", outerOaDeptDataParams);
            return Result.newError(ResultCodeEnum.OUTER_DEPTDATA_NOT_EXISTS);
        }

        // 获取字段映射
        Result<SystemFieldMappingResult> orInitFieldMapping = descManager.getOrInitFieldMapping(
                outerOaEnterpriseBindEntity, OuterOaConfigInfoTypeEnum.DEPARTMENT_OBJECT_LAYOUT_MAPPING);
        if (!orInitFieldMapping.isSuccess()) {
            log.warn("Get or init field mapping error: {}", orInitFieldMapping.getMsg());
            return Result.newError(ResultCodeEnum.PARAMETER_ERROR);
        }

        // 获取外部OA部门数据
        OuterOaDeptDataEntity outerOaDeptData = entities.get(0);
        if (outerOaDeptData == null || outerOaDeptData.getOutDeptInfo() == null) {
            log.warn("Outer department data not exists or outDeptInfo is null, outDeptId: {}", outDeptId);
            return Result.newError(ResultCodeEnum.OUTER_DEPTDATA_NOT_EXISTS);
        }

        // 获取字段映射关系
        SystemFieldMappingResult fieldMappingResult = orInitFieldMapping.getData();
        List<SystemFieldMappingResult.ItemFieldMapping> itemFieldMappings = fieldMappingResult.getItemFieldMappings();
        if (CollectionUtils.isEmpty(itemFieldMappings)) {
            log.warn("Field mapping is empty, outDeptId: {}", outDeptId);
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }

        // 创建CRM部门对象
        ObjectData objectData = new ObjectData();
        // 设置对象API名称为部门对象
        objectData.put("object_describe_api_name", AccountTypeEnum.DEPT_BIND.getCode());

        // 根据字段映射关系，设置CRM部门对象的字段值
        JSONObject outDeptInfo = outerOaDeptData.getOutDeptInfo();
        for (SystemFieldMappingResult.ItemFieldMapping mapping : itemFieldMappings) {
            String outerOAFieldApiName = mapping.getOuterOAFieldApiName();
            String crmFieldApiName = mapping.getCrmFieldApiName();

            if (outerOAFieldApiName != null && outDeptInfo.containsKey(outerOAFieldApiName)) {
                Object fieldValue = outDeptInfo.get(outerOAFieldApiName);
                // 不为空才传参
                if (fieldValue != null) {
                    objectData.put(crmFieldApiName, fieldValue);
                }
            }
        }

        // 设置默认必填字段
        if (!objectData.containsKey(CRMDepartmentFieldEnum.STATUS.getCode())) {
            objectData.put(CRMDepartmentFieldEnum.STATUS.getCode(), "1"); // 设置默认状态为启用
        }

        // 处理上级部门绑定关系
        if (outerOaDeptData.getParentDeptId() != null) {
            String parentOutDeptId = outerOaDeptData.getParentDeptId();
            OuterOaDepartmentBindEntity parentDeptBind = outerOaDepartmentBindManager.getByOutDeptId(
                    outerOaEnterpriseBindEntity.getChannel(), outerOaEnterpriseBindEntity.getOutEa(),outerOaEnterpriseBindEntity.getFsEa(),
                    outerOaEnterpriseBindEntity.getAppId(), parentOutDeptId);

            if (parentDeptBind != null) {
                objectData.put(CRMDepartmentFieldEnum.PARENT_DEPT_ID.getCode(), parentDeptBind.getFsDepId());
            }
        }

        return Result.newSuccess(objectData);
    }
    public ObjectData getObjectData(String objectApiName,String id,String fsEa){
        Integer tenantId=eieaConverter.enterpriseAccountToId(fsEa);
        HeaderObj headerObj=new HeaderObj(tenantId, CrmConstants.SYSTEM_USER);
        GetByIdArg getByIdArg=new GetByIdArg();
        getByIdArg.setDataId(id);
        getByIdArg.setDescribeApiName(objectApiName);
        com.fxiaoke.crmrestapi.common.result.Result<ObjectDataGetByIdV3Result> byId = objectDataServiceV3.getById(headerObj, getByIdArg);
        if(byId.isSuccess()){
            ObjectDataGetByIdV3Result data = byId.getData();
            return data.getObjectData();
        }
        return null;
    }

    public List<ObjectData> getCRmEmployeeByUserId(Integer tenantId,String objApiName,List<String> dataIds){

        HeaderObj headerObj = HeaderObj.newInstance(tenantId, ConstantDb.CRM_OPERATOR_USER_ID);
        FindV3Arg findV3Arg = new FindV3Arg();
        findV3Arg.setDescribeApiName(objApiName);
        // 返回查询字段

        List<String> selectFields = Lists.newArrayList();
        // 需要补充主属部门
        selectFields.add(CRMEmployeeFiledEnum.OWNER_DEPARTMENT.getCode());
        // selectFields.add(CRMEmployeeFiledEnum.MAIN_DEPARTMENT.getCode());
        selectFields.add(CRMEmployeeFiledEnum.STATUS.getCode());
        selectFields.add(CRMEmployeeFiledEnum.USER_ID.getCode());
        selectFields.add(CRMEmployeeFiledEnum.PHONE.getCode());
        selectFields.add(CRMEmployeeFiledEnum.NAME.getCode());
        findV3Arg.setSelectFields(selectFields);
        SearchTemplateQuery searchQueryInfo = new SearchTemplateQuery();
        searchQueryInfo.setPermissionType(0);
        searchQueryInfo.addFilter("user_id", Lists.newArrayList(dataIds), FilterOperatorEnum.IN);
        findV3Arg.setSearchQueryInfo(JSONObject.toJSONString(searchQueryInfo));
        com.fxiaoke.crmrestapi.common.result.Result<ObjectDataQueryListResult> objectDataQueryListResult = objectDataServiceV3
                .queryList(headerObj, findV3Arg);
        log.info("crmEmployee data:{}",objectDataQueryListResult);
        if(objectDataQueryListResult.isSuccess()){
            return objectDataQueryListResult.getData().getQueryResult().getDataList();
        }
        return null;
    }

    private  boolean hasSyncAccount(OuterOaEnterpriseBindEntity enterpriseBindEntity){
        SettingsResult settingBindRules = outerOaConfigInfoManager.getSettingBindRules(enterpriseBindEntity.getId());
        if(!settingBindRules.getSyncTypeEnum().equals(EnterpriseConfigAccountSyncTypeEnum.accountSync)){
            log.warn("enterprise not sync account :{}",enterpriseBindEntity);
            return  false;
        }
        return true;
    }

    private boolean hasManualAccount(OuterOaEnterpriseBindEntity enterpriseBindEntity){
        SettingsResult settingBindRules = outerOaConfigInfoManager.getSettingBindRules(enterpriseBindEntity.getId());
        if(!settingBindRules.getBindTypeEnum().equals(BindTypeEnum.manual)){
            log.warn("enterprise not manual account :{}",enterpriseBindEntity);
            return  false;
        }
        return true;
    }

}
