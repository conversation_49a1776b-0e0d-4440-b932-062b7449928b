package com.facishare.open.feishu.web.template;

import com.alibaba.fastjson.JSONObject;
import com.facishare.open.feishu.sync.manager.FeishuOuterOaConnectManager;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.order.contacts.proxy.api.result.Result;
import com.facishare.open.order.contacts.proxy.api.service.FsDepartmentServiceProxy;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.outer.oa.connector.common.api.admin.BaseConnectorVo;
import com.facishare.open.outer.oa.connector.common.api.enums.AlertTypeEnum;
import com.facishare.open.feishu.web.BaseTest;
import com.facishare.open.feishu.web.template.inner.calendar.FeishuCreateCalendarTemplate;
import com.facishare.open.feishu.web.template.inner.calendar.FeishuDeleteCalendarTemplate;
import com.facishare.open.feishu.web.template.inner.calendar.FeishuUpdateCalendarTemplate;
import com.facishare.open.feishu.web.template.model.CalendarObjModel;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.result.OuterOAConnectSettingResult;
import com.google.common.collect.Lists;
import org.apache.kafka.common.protocol.types.Field;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.List;

public class CalendarTemplateTest extends BaseTest {
    @Resource
    private FeishuCreateCalendarTemplate feishuCreateCalendarTemplate;
    @Resource
    private FeishuUpdateCalendarTemplate feishuUpdateCalendarTemplate;
    @Resource
    private FeishuDeleteCalendarTemplate feishuDeleteCalendarTemplate;
    @Resource
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Autowired
    private FeishuOuterOaConnectManager feishuOuterOaConnectManager;
    @Resource
    private FsDepartmentServiceProxy fsDepartmentServiceProxy;


    @Test
    public void feishuUpdateCalendarTemplate(){
        String channelName="feishu";
        ChannelEnum.valueOf(channelName);

        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntitiesByFsEa(ChannelEnum.feishu,"90429");
        for (OuterOaEnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
            CalendarObjModel calendarObjModel = new CalendarObjModel();
            calendarObjModel.setEnterpriseBindEntity(enterpriseBindEntity);
            calendarObjModel.setObjectId("676a17ed1ddb1e0001b9ab70");
            calendarObjModel.setEventType("u");
            feishuUpdateCalendarTemplate.execute(calendarObjModel);
        }
    }

    @Test
    public void feishuDeleteCalendarTemplate(){
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntitiesByFsEa(ChannelEnum.feishu,"90429");
        for (OuterOaEnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
            CalendarObjModel calendarObjModel = new CalendarObjModel();
            calendarObjModel.setEnterpriseBindEntity(enterpriseBindEntity);
            calendarObjModel.setObjectId("676a17ed1ddb1e0001b9ab70");
            calendarObjModel.setEventType("d");
            feishuDeleteCalendarTemplate.execute(calendarObjModel);
        }
    }
    @Test
    public void testData(){
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntitiesByFsEa(ChannelEnum.feishu,"91449");
        OuterOaEnterpriseBindEntity enterpriseBindEntity = enterpriseBindEntities.get(0);
        BaseConnectorVo baseConnectorVo = JSONObject.parseObject(enterpriseBindEntity.getConnectInfo(), enterpriseBindEntity.getChannel().getClassName());
        if(!baseConnectorVo.getAlertTypes().contains(AlertTypeEnum.CRM_SCHEDULE)){
            LogUtils.info("enterprise not CRM_SCHEDULE support todo:{}",enterpriseBindEntity.getFsEa());

        }
        String data="{\n" +
                "    \"connectParams\": {\n" +
                "            \"feishu\": {\n" +
                "                \"dataCenterId\": \"67e6862d20c3d0b0e330910d\",\n" +
                "                \"connectorName\": \"飞书\",\n" +
                "                \"channel\": \"feishu\",\n" +
                "                \"alertConfig\": false,\n" +
                "                \"enterpriseName\":\"飞书测试\",\n" +
                "                \"appId\":\"cli_a75815139bbc500e\",\n" +
                "                \"appSecret\":\"LZMQ9BHfCczQ4jeXlAsjkcXvzNAixLhg\"\n" +
                "               \n" +
                "            }\n" +
                "        },\n" +
                "        \"channelEnum\": \"feishu\",\n" +
                "        \"outerOaAppInfoTypeEnum\":\"selfBuild\",\n" +
                "        \"currentDcId\":\"67e6862d20c3d0b0e330910d\"\n" +
                "}";
        OuterOAConnectSettingResult outerOAConnectSettingResult= JSONObject.parseObject(data,OuterOAConnectSettingResult.class);
        feishuOuterOaConnectManager.doValidateConfigAndSave(outerOAConnectSettingResult, ChannelEnum.feishu, null);
    }

    @Test
    public void bulkStop() {
        Result<Void> result = fsDepartmentServiceProxy.bulkStop("88521", Lists.newArrayList("1001"));
        System.out.println(result);

    }
}
