package com.facishare.open.oa.base.dbproxy.pg.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEmployeeBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.mapper.OuterOaEmployeeBindMapper;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEmployeeBindParams;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.fxiaoke.api.IdGenerator;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Collections;
import java.util.Objects;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;

/**
 * Manager 类 - 员工绑定
 */
@Component
public class OuterOaEmployeeBindManager {

    @Resource
    private OuterOaEmployeeBindMapper outerOaEmployeeBindMapper;

    public Integer insert(OuterOaEmployeeBindEntity entity) {
        if (StringUtils.isEmpty(entity.getId())) {
            entity.setId(IdGenerator.get());
        }
        return outerOaEmployeeBindMapper.insert(entity);
    }

    public Integer updateById(OuterOaEmployeeBindEntity entity) {
        return outerOaEmployeeBindMapper.updateById(entity);
    }

    public Integer batchBindEmployeeList(List<OuterOaEmployeeBindEntity> employeeBindEntities) {
        // 加id
        for (OuterOaEmployeeBindEntity employeeBindEntity : employeeBindEntities) {
            if (StringUtils.isEmpty(employeeBindEntity.getId())) {
                employeeBindEntity.setId(IdGenerator.get());
            }
        }
        return outerOaEmployeeBindMapper.batchUpsert(employeeBindEntities);
    }

    public OuterOaEmployeeBindEntity getEmployeeBindEntity(ChannelEnum channel, String outEa, String fsEa, String appId,
                                                           String outEmpId) {
        LambdaQueryWrapper<OuterOaEmployeeBindEntity> wrapper = new LambdaQueryWrapper<>();
        if(ObjectUtils.isNotEmpty(channel)){
            wrapper.eq(OuterOaEmployeeBindEntity::getChannel, channel);
        }
        if(StringUtils.isNotEmpty(outEa)){
            wrapper.eq(OuterOaEmployeeBindEntity::getOutEa, outEa);
        }
        if(StringUtils.isNotEmpty(fsEa)){
            wrapper.eq(OuterOaEmployeeBindEntity::getFsEa, fsEa);
        }

        if(StringUtils.isNotEmpty(appId)){
            wrapper.eq(OuterOaEmployeeBindEntity::getAppId, appId);
        }
        if(StringUtils.isNotEmpty(outEmpId)){
            wrapper.eq(OuterOaEmployeeBindEntity::getOutEmpId, outEmpId);
        }
        return outerOaEmployeeBindMapper.selectOne(wrapper);
    }

    public OuterOaEmployeeBindEntity getNormalEmployeeBindEntity(ChannelEnum channel, String outEa, String fsEa, String appId,
                                                           String outEmpId) {
        LambdaQueryWrapper<OuterOaEmployeeBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaEmployeeBindEntity::getChannel, channel);
        wrapper.eq(OuterOaEmployeeBindEntity::getOutEa, outEa);
        wrapper.eq(OuterOaEmployeeBindEntity::getFsEa, fsEa);
        wrapper.eq(OuterOaEmployeeBindEntity::getAppId, appId);
        wrapper.eq(OuterOaEmployeeBindEntity::getOutEmpId, outEmpId);
        wrapper.eq(OuterOaEmployeeBindEntity::getBindStatus, BindStatusEnum.normal);
        return outerOaEmployeeBindMapper.selectOne(wrapper);
    }

    // updateStatusByFsEmpIds
    public Integer updateStatusByFsEmpIds(ChannelEnum channel, String outEa, String fsEa, String appId,
            List<String> fsEmpIds, BindStatusEnum status) {
        LambdaUpdateWrapper<OuterOaEmployeeBindEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(OuterOaEmployeeBindEntity::getChannel, channel);
        wrapper.eq(OuterOaEmployeeBindEntity::getOutEa, outEa);
        wrapper.eq(OuterOaEmployeeBindEntity::getFsEa, fsEa);
        wrapper.eq(OuterOaEmployeeBindEntity::getAppId, appId);
        wrapper.in(OuterOaEmployeeBindEntity::getFsEmpId, fsEmpIds);
        wrapper.set(OuterOaEmployeeBindEntity::getBindStatus, status);
        return outerOaEmployeeBindMapper.update(null, wrapper);
    }

    // 如果对应的外部员工已有绑定关系，则返回对应的fsEmpId
    public String getFsEmpIdByEaAndOutEmpId(ChannelEnum channel, String appId, String fsEa, String outEa, String outEmpId) {
        LambdaQueryWrapper<OuterOaEmployeeBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaEmployeeBindEntity::getChannel, channel);
        wrapper.eq(OuterOaEmployeeBindEntity::getOutEa, outEa);
        wrapper.eq(OuterOaEmployeeBindEntity::getFsEa, fsEa);
        wrapper.eq(OuterOaEmployeeBindEntity::getOutEmpId, outEmpId);
        wrapper.eq(OuterOaEmployeeBindEntity::getAppId, appId);
        final OuterOaEmployeeBindEntity outerOaEmployeeBindEntity = outerOaEmployeeBindMapper.selectOne(wrapper);
        return Objects.isNull(outerOaEmployeeBindEntity) ? null : outerOaEmployeeBindEntity.getFsEmpId();
    }

    // 如果对应的外部员工已有绑定关系，则返回对应的fsEmpId
    public List<OuterOaEmployeeBindEntity> getByEaAndOutEmpId(ChannelEnum channel, String fsEa, String outEa, String outEmpId) {
        LambdaQueryWrapper<OuterOaEmployeeBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaEmployeeBindEntity::getChannel, channel);
        wrapper.eq(OuterOaEmployeeBindEntity::getOutEa, outEa);
        wrapper.eq(OuterOaEmployeeBindEntity::getFsEa, fsEa);
        wrapper.eq(OuterOaEmployeeBindEntity::getOutEmpId, outEmpId);
        wrapper.eq(OuterOaEmployeeBindEntity::getBindStatus, BindStatusEnum.normal);
        return outerOaEmployeeBindMapper.selectList(wrapper);
    }

    // batchGetEmployeeBindEntity
    public List<OuterOaEmployeeBindEntity> batchGetEmployeeBindEntity(ChannelEnum channel, String outEa, String fsEa,
                                                                      String appId, List<String> fsEmpIds, List<String> outEmpIds) {
        LambdaQueryWrapper<OuterOaEmployeeBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaEmployeeBindEntity::getChannel, channel);
        if (StringUtils.isNotEmpty(outEa)) {
            wrapper.eq(OuterOaEmployeeBindEntity::getOutEa, outEa);
        }
        if (StringUtils.isNotEmpty(fsEa)) {
            wrapper.eq(OuterOaEmployeeBindEntity::getFsEa, fsEa);
        }
        if (StringUtils.isNotEmpty(appId)) {
            wrapper.eq(OuterOaEmployeeBindEntity::getAppId, appId);
        }
        if (CollectionUtils.isNotEmpty(fsEmpIds)) {
            wrapper.in(OuterOaEmployeeBindEntity::getFsEmpId, fsEmpIds);
        }
        if (CollectionUtils.isNotEmpty(outEmpIds)) {
            wrapper.in(OuterOaEmployeeBindEntity::getOutEmpId, outEmpIds);
        }
        return outerOaEmployeeBindMapper.selectList(wrapper);
    }

    public List<OuterOaEmployeeBindEntity> batchGetNormalEmployeeBindEntity(ChannelEnum channel, String outEa, String fsEa,
                                                                      String appId, List<String> fsEmpIds, List<String> outEmpIds) {
        LambdaQueryWrapper<OuterOaEmployeeBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaEmployeeBindEntity::getChannel, channel);
        wrapper.eq(OuterOaEmployeeBindEntity::getBindStatus, BindStatusEnum.normal);
        if (StringUtils.isNotEmpty(outEa)) {
            wrapper.eq(OuterOaEmployeeBindEntity::getOutEa, outEa);
        }
        if (StringUtils.isNotEmpty(fsEa)) {
            wrapper.eq(OuterOaEmployeeBindEntity::getFsEa, fsEa);
        }
        if (StringUtils.isNotEmpty(appId)) {
            wrapper.eq(OuterOaEmployeeBindEntity::getAppId, appId);
        }
        if (CollectionUtils.isNotEmpty(fsEmpIds)) {
            wrapper.in(OuterOaEmployeeBindEntity::getFsEmpId, fsEmpIds);
        }
        if (CollectionUtils.isNotEmpty(outEmpIds)) {
            wrapper.in(OuterOaEmployeeBindEntity::getOutEmpId, outEmpIds);
        }
        return outerOaEmployeeBindMapper.selectList(wrapper);
    }

    public List<OuterOaEmployeeBindEntity> getEmployeeBindEntitiesByFsEmpId(ChannelEnum channel, String fsEa,
            String fsEmpId) {
        LambdaQueryWrapper<OuterOaEmployeeBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaEmployeeBindEntity::getChannel, channel);
        wrapper.eq(OuterOaEmployeeBindEntity::getFsEa, fsEa);
        wrapper.eq(OuterOaEmployeeBindEntity::getFsEmpId, fsEmpId);
        return outerOaEmployeeBindMapper.selectList(wrapper);
    }

    public Integer deleteEmployeeBindEntity(ChannelEnum channel, String outEa, String fsEa, String appId,
            List<String> fsEmpIds, List<String> outEmpIds) {
        LambdaQueryWrapper<OuterOaEmployeeBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaEmployeeBindEntity::getChannel, channel);
        wrapper.eq(OuterOaEmployeeBindEntity::getOutEa, outEa);
        wrapper.eq(OuterOaEmployeeBindEntity::getFsEa, fsEa);
        if (StringUtils.isNotEmpty(appId)) {
            wrapper.eq(OuterOaEmployeeBindEntity::getAppId, appId);
        }
        // 判断
        if (CollectionUtils.isNotEmpty(fsEmpIds)) {
            wrapper.in(OuterOaEmployeeBindEntity::getFsEmpId, fsEmpIds);
        }
        if (CollectionUtils.isNotEmpty(outEmpIds)) {
            wrapper.in(OuterOaEmployeeBindEntity::getOutEmpId, outEmpIds);
        }
        return outerOaEmployeeBindMapper.delete(wrapper);
    }

    /**
     *
     * 根据参数查询员工绑定信息列表 根据参数查询员工绑定信息列表，支持分页
     *
     * @param params 查询参数
     * @return 员工绑定实体列表
     */
    public List<OuterOaEmployeeBindEntity> getEntities(OuterOaEmployeeBindParams params) {
        LambdaQueryWrapper<OuterOaEmployeeBindEntity> wrapper = getOuterOaEmployeeBindEntityLambdaQueryWrapper(params);
        // 添加按更新时间降序排序
        wrapper.orderByDesc(OuterOaEmployeeBindEntity::getUpdateTime);

        // 使用分页查询，确保页码和页大小参数有效
        int pageNum = params.getPage() <= 0 ? 1 : params.getPage();
        int pageSize = params.getPageSize() <= 0 ? 10 : params.getPageSize();

        // 显式设置searchCount为true，强制进行count查询
        Page<OuterOaEmployeeBindEntity> page = new Page<>(pageNum, pageSize, true);
        Page<OuterOaEmployeeBindEntity> result = outerOaEmployeeBindMapper.selectPage(page, wrapper);

        return result.getRecords();
    }

    /**
     *
     * 根据参数查询员工绑定信息列表 根据参数查询员工绑定信息列表，支持分页 不依赖MybatisPlus分页插件，手动实现分页逻辑
     *
     * @param params 查询参数
     * @return 员工绑定实体列表
     */
    public Page<OuterOaEmployeeBindEntity> getPageEntities(OuterOaEmployeeBindParams params) {
        try {
            // 构建查询条件
            LambdaQueryWrapper<OuterOaEmployeeBindEntity> wrapper = getOuterOaEmployeeBindEntityLambdaQueryWrapper(
                    params);

            // 确保页码和页大小参数有效
            int pageNum = params.getPage() <= 0 ? 1 : params.getPage();
            int pageSize = params.getPageSize() <= 0 ? 10 : params.getPageSize();

            // 1. 手动执行count查询获取总记录数
            Long total = outerOaEmployeeBindMapper.selectCount(wrapper);

            // 2. 计算总页数
            long pages = 0;
            if (total > 0) {
                pages = total / pageSize;
                if (total % pageSize != 0) {
                    pages++;
                }
            }

            // 3. 计算偏移量，用于SQL分页
            long offset = (pageNum - 1) * pageSize;

            // 4. 手动添加分页条件
            wrapper.last("LIMIT " + pageSize + " OFFSET " + offset);
            // 添加按更新时间降序排序
            wrapper.orderByDesc(OuterOaEmployeeBindEntity::getUpdateTime);
            // 5. 执行查询获取当前页数据
            List<OuterOaEmployeeBindEntity> records = outerOaEmployeeBindMapper.selectList(wrapper);

            // 6. 手动构建Page对象
            Page<OuterOaEmployeeBindEntity> result = new Page<>(pageNum, pageSize);
            result.setTotal(total);
            result.setPages(pages);
            result.setRecords(records);
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            // 创建空结果并返回，避免空指针异常
            return new Page<>();
        }
    }

    /**
     *
     * 根据参数查询员工绑定信息列表 根据参数查询员工绑定信息列表，支持分页
     *
     * @param params 查询参数
     * @return 员工绑定实体列表
     */
    public Long getAllEntities(OuterOaEmployeeBindParams params) {
        LambdaQueryWrapper<OuterOaEmployeeBindEntity> wrapper = getOuterOaEmployeeBindEntityLambdaQueryWrapper(params);
        Long total = outerOaEmployeeBindMapper.selectCount(wrapper);
        return total;
    }

    @NotNull
    private static LambdaQueryWrapper<OuterOaEmployeeBindEntity> getOuterOaEmployeeBindEntityLambdaQueryWrapper(
            OuterOaEmployeeBindParams params) {
        // 设置默认分页参数
        LambdaQueryWrapper<OuterOaEmployeeBindEntity> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(params.getId())) {
            wrapper.eq(OuterOaEmployeeBindEntity::getId, params.getId());
        }
        if (params.getChannel() != null) {
            wrapper.eq(OuterOaEmployeeBindEntity::getChannel, params.getChannel());
        }
        if (StringUtils.isNotEmpty(params.getFsEa())) {
            wrapper.eq(OuterOaEmployeeBindEntity::getFsEa, params.getFsEa());
        }
        if (StringUtils.isNotEmpty(params.getOutEa())) {
            wrapper.eq(OuterOaEmployeeBindEntity::getOutEa, params.getOutEa());
        }
        if (StringUtils.isNotEmpty(params.getAppId())) {
            wrapper.eq(OuterOaEmployeeBindEntity::getAppId, params.getAppId());
        }
        if (StringUtils.isNotEmpty(params.getFsEmpId())) {
            wrapper.eq(OuterOaEmployeeBindEntity::getFsEmpId, params.getFsEmpId());
        }
        if (StringUtils.isNotEmpty(params.getOutEmpId())) {
            wrapper.eq(OuterOaEmployeeBindEntity::getOutEmpId, params.getOutEmpId());
        }
        if (params.getBindStatus() != null) {
            wrapper.eq(OuterOaEmployeeBindEntity::getBindStatus, params.getBindStatus());
        }
        if (StringUtils.isNotEmpty(params.getDcId())) {
            wrapper.eq(OuterOaEmployeeBindEntity::getDcId, params.getDcId());
        }
        return wrapper;
    }

    /**
     * 根据参数查询员工绑定信息列表，支持分页
     *
     * @param params 查询参数
     * @return 员工绑定实体列表
     */
    public List<OuterOaEmployeeBindEntity> getEntitiesByNotPage(OuterOaEmployeeBindParams params) {
        // 设置默认分页参数
        LambdaQueryWrapper<OuterOaEmployeeBindEntity> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(params.getId())) {
            wrapper.eq(OuterOaEmployeeBindEntity::getId, params.getId());
        }
        if (params.getChannel() != null) {
            wrapper.eq(OuterOaEmployeeBindEntity::getChannel, params.getChannel());
        }
        if (StringUtils.isNotEmpty(params.getFsEa())) {
            wrapper.eq(OuterOaEmployeeBindEntity::getFsEa, params.getFsEa());
        }
        if (StringUtils.isNotEmpty(params.getOutEa())) {
            wrapper.eq(OuterOaEmployeeBindEntity::getOutEa, params.getOutEa());
        }
        if (StringUtils.isNotEmpty(params.getAppId())) {
            wrapper.eq(OuterOaEmployeeBindEntity::getAppId, params.getAppId());
        }
        if (StringUtils.isNotEmpty(params.getFsEmpId())) {
            wrapper.eq(OuterOaEmployeeBindEntity::getFsEmpId, params.getFsEmpId());
        }
        if (StringUtils.isNotEmpty(params.getOutEmpId())) {
            wrapper.eq(OuterOaEmployeeBindEntity::getOutEmpId, params.getOutEmpId());
        }
        if (params.getBindStatus() != null) {
            wrapper.eq(OuterOaEmployeeBindEntity::getBindStatus, params.getBindStatus());
        }
        if (StringUtils.isNotEmpty(params.getDcId())) {
            wrapper.eq(OuterOaEmployeeBindEntity::getDcId, params.getDcId());
        }
        // 添加按更新时间降序排序
        wrapper.orderByDesc(OuterOaEmployeeBindEntity::getUpdateTime);

        List<OuterOaEmployeeBindEntity> result = outerOaEmployeeBindMapper.selectList(wrapper);
        return result;
    }

    /**
     * 根据fsEmpId列表和dcId查询员工绑定信息
     *
     * @param fsEmpIds fsEmpId列表
     * @param dcId     数据中心id
     * @return 员工绑定实体列表
     */
    public List<OuterOaEmployeeBindEntity> getEntitiesByFsEmpIds(List<String> fsEmpIds, String dcId) {
        if (fsEmpIds == null || fsEmpIds.isEmpty() || StringUtils.isEmpty(dcId)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<OuterOaEmployeeBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(OuterOaEmployeeBindEntity::getFsEmpId, fsEmpIds).eq(OuterOaEmployeeBindEntity::getDcId, dcId)
                .orderByDesc(OuterOaEmployeeBindEntity::getUpdateTime);

        return outerOaEmployeeBindMapper.selectList(wrapper);
    }

    /**
     * 根据外部员工ID列表查询员工绑定信息列表
     *
     * @param params    查询参数
     * @param outEmpIds 外部员工ID列表
     * @return 员工绑定实体列表
     */
    public List<OuterOaEmployeeBindEntity> getEntitiesByEmpIds(OuterOaEmployeeBindParams params, List<String> outEmpIds,
            List<String> fsEmpIds) {
        LambdaQueryWrapper<OuterOaEmployeeBindEntity> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(params.getId())) {
            wrapper.eq(OuterOaEmployeeBindEntity::getId, params.getId());
        }
        if (params.getChannel() != null) {
            wrapper.eq(OuterOaEmployeeBindEntity::getChannel, params.getChannel());
        }
        if (StringUtils.isNotEmpty(params.getFsEa())) {
            wrapper.eq(OuterOaEmployeeBindEntity::getFsEa, params.getFsEa());
        }
        if (StringUtils.isNotEmpty(params.getOutEa())) {
            wrapper.eq(OuterOaEmployeeBindEntity::getOutEa, params.getOutEa());
        }
        if (StringUtils.isNotEmpty(params.getAppId())) {
            wrapper.eq(OuterOaEmployeeBindEntity::getAppId, params.getAppId());
        }
        if (StringUtils.isNotEmpty(params.getFsEmpId())) {
            wrapper.eq(OuterOaEmployeeBindEntity::getFsEmpId, params.getFsEmpId());
        }
        if (params.getBindStatus() != null) {
            wrapper.eq(OuterOaEmployeeBindEntity::getBindStatus, params.getBindStatus());
        }
        if (StringUtils.isNotEmpty(params.getDcId())) {
            wrapper.eq(OuterOaEmployeeBindEntity::getDcId, params.getDcId());
        }
        if (outEmpIds != null && !outEmpIds.isEmpty()) {
            wrapper.in(OuterOaEmployeeBindEntity::getOutEmpId, outEmpIds);
        }
        if (fsEmpIds != null && !fsEmpIds.isEmpty()) {
            wrapper.in(OuterOaEmployeeBindEntity::getFsEmpId, fsEmpIds);
        }
        return outerOaEmployeeBindMapper.selectList(wrapper);
    }

    /**
     * 根据外部员工ID或飞书员工ID查询员工绑定信息
     */
    public List<OuterOaEmployeeBindEntity> getEntitiesOrOuterFsEmpId(String dataCenterId, String fsEmpId,
            String outUserId) {

        LambdaQueryWrapper<OuterOaEmployeeBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaEmployeeBindEntity::getDcId, dataCenterId);
        wrapper.and(w -> w.eq(OuterOaEmployeeBindEntity::getFsEmpId, fsEmpId).or()
                .eq(OuterOaEmployeeBindEntity::getOutEmpId, outUserId));
        return outerOaEmployeeBindMapper.selectList(wrapper);

    }

    /**
     * 批量插入或更新员工绑定信息
     *
     * @param entities 员工绑定信息实体列表
     * @return 成功插入或更新的记录数
     */
    public Integer batchUpsert(List<OuterOaEmployeeBindEntity> entities) {
        if (CollectionUtils.isEmpty(entities)) {
            return 0;
        }

        // 使用Map进行去重，保留最新的记录
        // 修复：使用dc_id和out_emp_id组合作为key，与数据库唯一约束保持一致
        Map<String, OuterOaEmployeeBindEntity> deduplicatedMap = new HashMap<>();
        for (OuterOaEmployeeBindEntity entity : entities) {
            if (StringUtils.isEmpty(entity.getId())) {
                entity.setId(IdGenerator.get());
            }
            if (entity.getCreateTime() == null) {
                entity.setCreateTime(System.currentTimeMillis());
            }
            entity.setUpdateTime(System.currentTimeMillis());

            // 修复：使用dc_id和out_emp_id组合作为key，与数据库唯一约束outer_oa_employee_bind_dc_id_out_emp_id_unique_idx保持一致
            String key = entity.getDcId() + ":" + entity.getOutEmpId();
            OuterOaEmployeeBindEntity existing = deduplicatedMap.get(key);
            if (existing == null || entity.getUpdateTime() > existing.getUpdateTime()) {
                deduplicatedMap.put(key, entity);
            }
        }

        // 转换回List
        List<OuterOaEmployeeBindEntity> deduplicatedEntities = new ArrayList<>(deduplicatedMap.values());

        if (deduplicatedEntities.isEmpty()) {
            return 0;
        }

        return outerOaEmployeeBindMapper.batchUpsert(deduplicatedEntities);
    }

    public Integer upsert(OuterOaEmployeeBindEntity entity) {
        if (Objects.isNull(entity)) {
            return 0;
        }
        return batchUpsert(Lists.newArrayList(entity));
    }

    /**
     * 根据渠道、fsEa和appId查询绑定关系
     */
    public List<OuterOaEmployeeBindEntity> queryByFsEa(ChannelEnum channel, String fsEa, String appId) {
        return queryByFsEa(channel, fsEa, appId, BindStatusEnum.normal);
    }

    public List<OuterOaEmployeeBindEntity> queryByFsEa(ChannelEnum channel, String fsEa, String appId,
            BindStatusEnum bindStatus) {
        LambdaQueryWrapper<OuterOaEmployeeBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaEmployeeBindEntity::getChannel, channel).eq(OuterOaEmployeeBindEntity::getFsEa, fsEa)
                .eq(OuterOaEmployeeBindEntity::getAppId, appId);
        if (bindStatus != null) {
            wrapper.eq(OuterOaEmployeeBindEntity::getBindStatus, bindStatus);
        }
        return outerOaEmployeeBindMapper.selectList(wrapper);
    }

    /**
     * 根据渠道、fsEa、appId和outEmpId查询绑定关系
     */
    public OuterOaEmployeeBindEntity queryByFsEaAndOutEmpId(ChannelEnum channel, String fsEa, String appId,
                                                            String outEmpId) {
        LambdaQueryWrapper<OuterOaEmployeeBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaEmployeeBindEntity::getChannel, channel)
                .eq(OuterOaEmployeeBindEntity::getFsEa, fsEa)
                .eq(OuterOaEmployeeBindEntity::getAppId, appId)
                .eq(OuterOaEmployeeBindEntity::getOutEmpId, outEmpId);
        return outerOaEmployeeBindMapper.selectOne(wrapper);
    }

    /**
     * 根据渠道、fsEa、appId和outEmpId查询绑定关系
     */
    public OuterOaEmployeeBindEntity queryNormalByFsEaAndOutEmpId(ChannelEnum channel, String fsEa, String appId,
                                                            String outEmpId) {
        LambdaQueryWrapper<OuterOaEmployeeBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaEmployeeBindEntity::getChannel, channel)
                .eq(OuterOaEmployeeBindEntity::getFsEa, fsEa)
                .eq(OuterOaEmployeeBindEntity::getAppId, appId)
                .eq(OuterOaEmployeeBindEntity::getOutEmpId, outEmpId)
                .eq(OuterOaEmployeeBindEntity::getBindStatus, BindStatusEnum.normal);
        ;
        return outerOaEmployeeBindMapper.selectOne(wrapper);
    }

    /**
     * 根据渠道、fsEa、appId和fsEmpId查询绑定关系
     */
    public OuterOaEmployeeBindEntity queryByFsEmpId(ChannelEnum channel, String fsEa, String appId, String fsEmpId) {
        LambdaQueryWrapper<OuterOaEmployeeBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaEmployeeBindEntity::getChannel, channel)
                .eq(OuterOaEmployeeBindEntity::getFsEa, fsEa)
                .eq(OuterOaEmployeeBindEntity::getAppId, appId)
                .eq(OuterOaEmployeeBindEntity::getFsEmpId, fsEmpId)
                .eq(OuterOaEmployeeBindEntity::getBindStatus, BindStatusEnum.normal);
        return outerOaEmployeeBindMapper.selectOne(wrapper);
    }

    /**
     * 根据id删除绑定关系
     */
    public Integer deleteById(String id) {
        return outerOaEmployeeBindMapper.deleteById(id);
    }

    /**
     * 根据id列表批量删除绑定关系
     *
     * @param ids 需要删除的记录ID列表
     * @return 成功删除的记录数
     */
    public Integer batchDeleteByIds(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return 0;
        }

        LambdaQueryWrapper<OuterOaEmployeeBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(OuterOaEmployeeBindEntity::getId, ids);
        return outerOaEmployeeBindMapper.delete(wrapper);
    }

    /**
     * 根据渠道、fsEa、appId和outEmpId删除绑定关系
     */
    public Integer deleteByOutEmpId(ChannelEnum channel, String fsEa, String appId, String outEmpId) {
        LambdaQueryWrapper<OuterOaEmployeeBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaEmployeeBindEntity::getChannel, channel).eq(OuterOaEmployeeBindEntity::getFsEa, fsEa)
                .eq(OuterOaEmployeeBindEntity::getAppId, appId).eq(OuterOaEmployeeBindEntity::getOutEmpId, outEmpId);
        return outerOaEmployeeBindMapper.delete(wrapper);
    }

    /**
     * 保存绑定关系
     */
    public Integer save(OuterOaEmployeeBindEntity entity) {
        if (StringUtils.isEmpty(entity.getId())) {
            return insert(entity);
        } else {
            return updateById(entity);
        }
    }

    public Integer updateStatusByFsEaAndOutEmpId(ChannelEnum channel, String fsEa, String appId, String outEmpId,
            BindStatusEnum status) {
        return outerOaEmployeeBindMapper.updateStatusByOutEmpId(channel, fsEa, appId, outEmpId, status);
    }

    public Integer updateStatusByOutEaAndOutEmpId(ChannelEnum channel, String outEa, String appId, String outEmpId,
            BindStatusEnum status) {
        return outerOaEmployeeBindMapper.updateStatusByOutEaAndOutEmpId(channel, outEa, appId, outEmpId, status);
    }

    /**
     * 根据参数获取正常状态(normal)的员工绑定列表 可根据outEa、fsEa、outEmpId、fsEmpId等参数进行筛选
     *
     * @param channel  渠道类型
     * @param outEa    外部企业EA
     * @param fsEa     纷享企业EA
     * @param outEmpId 外部员工ID
     * @param fsEmpId  纷享员工ID
     *
     * @return 符合条件的员工绑定列表
     */
    public List<OuterOaEmployeeBindEntity> getNormalStatusEmployees(ChannelEnum channel, String outEa, String fsEa,
            String outEmpId, String fsEmpId) {
        LambdaQueryWrapper<OuterOaEmployeeBindEntity> wrapper = new LambdaQueryWrapper<>();

        // 设置必要条件：绑定状态为正常
        wrapper.eq(OuterOaEmployeeBindEntity::getBindStatus, BindStatusEnum.normal);

        // 设置渠道条件（如果提供）
        if (channel != null) {
            wrapper.eq(OuterOaEmployeeBindEntity::getChannel, channel);
        }

        // 设置外部企业EA条件（如果提供）
        if (StringUtils.isNotEmpty(outEa)) {
            wrapper.eq(OuterOaEmployeeBindEntity::getOutEa, outEa);
        }

        // 设置纷享企业EA条件（如果提供）
        if (StringUtils.isNotEmpty(fsEa)) {
            wrapper.eq(OuterOaEmployeeBindEntity::getFsEa, fsEa);
        }

        // 设置外部员工ID条件（如果提供）
        if (StringUtils.isNotEmpty(outEmpId)) {
            wrapper.eq(OuterOaEmployeeBindEntity::getOutEmpId, outEmpId);
        }

        // 设置纷享员工ID条件（如果提供）
        if (StringUtils.isNotEmpty(fsEmpId)) {
            wrapper.eq(OuterOaEmployeeBindEntity::getFsEmpId, fsEmpId);
        }

        // 添加按更新时间降序排序
        wrapper.orderByDesc(OuterOaEmployeeBindEntity::getUpdateTime);

        return outerOaEmployeeBindMapper.selectList(wrapper);
    }

    /**
     * 有些场景，判断人员是不是存在的时候，涉及多应用的时候，需要去掉appid. 因为存在多应用，同一个人员可能在不同的应用中，所以需要去掉appid.
     *
     * @param params
     * @return
     */
    public List<OuterOaEmployeeBindEntity> getUserNotDependAppid(OuterOaEmployeeBindParams params) {
        if (params == null) {
            return null;
        }
        params.setAppId(null);
        return getEntities(params);
    }

    /**
     * 根据参数对象批量更新员工绑定状态 支持通过各种条件组合过滤要更新的记录
     *
     * @param params           查询参数对象，用于定位需要更新的记录
     * @param targetBindStatus 目标绑定状态
     * @param fsEmpIds         可选的纷享员工ID列表，为空则不过滤
     * @param outEmpIds        可选的外部员工ID列表，为空则不过滤
     * @return 更新的记录数
     */
    public Integer batchUpdateBindStatusByParams(OuterOaEmployeeBindParams params, BindStatusEnum targetBindStatus,
            List<String> fsEmpIds, List<String> outEmpIds) {
        if (params == null || targetBindStatus == null) {
            return 0;
        }

        LambdaUpdateWrapper<OuterOaEmployeeBindEntity> wrapper = new LambdaUpdateWrapper<>();

        // 添加查询条件
        if (params.getChannel() != null) {
            wrapper.eq(OuterOaEmployeeBindEntity::getChannel, params.getChannel());
        }
        if (StringUtils.isNotEmpty(params.getFsEa())) {
            wrapper.eq(OuterOaEmployeeBindEntity::getFsEa, params.getFsEa());
        }
        if (StringUtils.isNotEmpty(params.getOutEa())) {
            wrapper.eq(OuterOaEmployeeBindEntity::getOutEa, params.getOutEa());
        }
        if (StringUtils.isNotEmpty(params.getAppId())) {
            wrapper.eq(OuterOaEmployeeBindEntity::getAppId, params.getAppId());
        }
        if (StringUtils.isNotEmpty(params.getDcId())) {
            wrapper.eq(OuterOaEmployeeBindEntity::getDcId, params.getDcId());
        }
        if (StringUtils.isNotEmpty(params.getFsEmpId())) {
            wrapper.eq(OuterOaEmployeeBindEntity::getFsEmpId, params.getFsEmpId());
        }
        if (StringUtils.isNotEmpty(params.getOutEmpId())) {
            wrapper.eq(OuterOaEmployeeBindEntity::getOutEmpId, params.getOutEmpId());
        }
        if (params.getBindStatus() != null) {
            wrapper.eq(OuterOaEmployeeBindEntity::getBindStatus, params.getBindStatus());
        }

        // 添加fsEmpIds过滤条件（如果提供）
        if (CollectionUtils.isNotEmpty(fsEmpIds)) {
            wrapper.in(OuterOaEmployeeBindEntity::getFsEmpId, fsEmpIds);
        }

        // 添加outEmpIds过滤条件（如果提供）
        if (CollectionUtils.isNotEmpty(outEmpIds)) {
            wrapper.in(OuterOaEmployeeBindEntity::getOutEmpId, outEmpIds);
        }

        // 设置更新内容
        OuterOaEmployeeBindEntity updateEntity = new OuterOaEmployeeBindEntity();
        updateEntity.setBindStatus(targetBindStatus);
        updateEntity.setUpdateTime(System.currentTimeMillis());

        return outerOaEmployeeBindMapper.update(updateEntity, wrapper);
    }

    /**
     * 批量更新员工绑定状态，支持渠道参数
     *
     * @param channel    渠道类型，必填参数
     * @param fsEa       纷享企业账号，必填参数
     * @param fsEmpIds   纷享员工ID列表，必填参数
     * @param bindStatus 绑定状态，必填参数
     * @param outEa      外部企业账号，可选参数
     * @param appId      应用ID，可选参数
     * @return 更新的记录数
     */
    public Integer batchUpdateEmpStatus(ChannelEnum channel, String fsEa, List<String> fsEmpIds,
            BindStatusEnum bindStatus, String outEa, String appId) {
        // 参数验证
        if (channel == null || StringUtils.isEmpty(fsEa) || CollectionUtils.isEmpty(fsEmpIds) || bindStatus == null) {
            return 0;
        }

        LambdaUpdateWrapper<OuterOaEmployeeBindEntity> wrapper = new LambdaUpdateWrapper<>();
        // 必填条件
        wrapper.eq(OuterOaEmployeeBindEntity::getChannel, channel);
        wrapper.eq(OuterOaEmployeeBindEntity::getFsEa, fsEa);
        wrapper.in(OuterOaEmployeeBindEntity::getFsEmpId, fsEmpIds);

        // 可选条件
        if (StringUtils.isNotEmpty(outEa)) {
            wrapper.eq(OuterOaEmployeeBindEntity::getOutEa, outEa);
        }
        if (StringUtils.isNotEmpty(appId)) {
            wrapper.eq(OuterOaEmployeeBindEntity::getAppId, appId);
        }

        // 设置更新内容
        OuterOaEmployeeBindEntity updateEntity = new OuterOaEmployeeBindEntity();
        updateEntity.setBindStatus(bindStatus);
        // 使用系统当前时间戳作为更新时间
        long currentTimeMillis = System.currentTimeMillis();
        updateEntity.setUpdateTime(currentTimeMillis);

        return outerOaEmployeeBindMapper.update(updateEntity, wrapper);
    }

    /**
     * 批量更新员工绑定状态（旧版无channel参数）
     *
     * @param fsEa       纷享企业账号
     * @param fsEmpIds   纷享员工ID列表
     * @param bindStatus 绑定状态
     * @param outEa      外部企业账号
     * @param appId      应用ID
     * @return 更新的记录数
     * @deprecated 请使用
     *             {@link #batchUpdateBindStatus(ChannelEnum, String, List, BindStatusEnum, String, String)}
     *             代替
     */
    @Deprecated
    public Integer batchUpdateBindStatus(String fsEa, List<String> fsEmpIds, BindStatusEnum bindStatus, String outEa,
            String appId) {
        // 为了向后兼容，默认不指定channel的情况下不对channel做过滤
        if (StringUtils.isEmpty(fsEa) || fsEmpIds == null || fsEmpIds.isEmpty() || bindStatus == null) {
            return 0;
        }

        LambdaUpdateWrapper<OuterOaEmployeeBindEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(OuterOaEmployeeBindEntity::getFsEa, fsEa).in(OuterOaEmployeeBindEntity::getFsEmpId, fsEmpIds);

        if (StringUtils.isNotEmpty(outEa)) {
            wrapper.eq(OuterOaEmployeeBindEntity::getOutEa, outEa);
        }
        if (StringUtils.isNotEmpty(appId)) {
            wrapper.eq(OuterOaEmployeeBindEntity::getAppId, appId);
        }

        OuterOaEmployeeBindEntity updateEntity = new OuterOaEmployeeBindEntity();
        updateEntity.setBindStatus(bindStatus);
        updateEntity.setUpdateTime(System.currentTimeMillis());

        return outerOaEmployeeBindMapper.update(updateEntity, wrapper);
    }

    public Integer updateEmpStatusById(String id, BindStatusEnum bindStatus, String fsEa) {
        if (StringUtils.isEmpty(id)) {
            return null;
        }
        LambdaQueryWrapper<OuterOaEmployeeBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaEmployeeBindEntity::getId, id);

        // 只有当fsEa不为空时才添加条件
        if (StringUtils.isNotEmpty(fsEa)) {
            wrapper.eq(OuterOaEmployeeBindEntity::getFsEa, fsEa);
        }

        OuterOaEmployeeBindEntity updateEntity = new OuterOaEmployeeBindEntity();
        updateEntity.setBindStatus(bindStatus);
        updateEntity.setUpdateTime(System.currentTimeMillis());
        return outerOaEmployeeBindMapper.update(updateEntity, wrapper);
    }

    public Integer updateEmpStatusByCrmUserId(String crmUserId , BindStatusEnum bindStatus, String fsEa) {
        if (StringUtils.isEmpty(crmUserId)) {
            return null;
        }
        LambdaQueryWrapper<OuterOaEmployeeBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaEmployeeBindEntity::getFsEmpId, crmUserId);

        // 只有当fsEa不为空时才添加条件
        if (StringUtils.isNotEmpty(fsEa)) {
            wrapper.eq(OuterOaEmployeeBindEntity::getFsEa, fsEa);
        }

        OuterOaEmployeeBindEntity updateEntity = new OuterOaEmployeeBindEntity();
        updateEntity.setBindStatus(bindStatus);
        updateEntity.setUpdateTime(System.currentTimeMillis());
        return outerOaEmployeeBindMapper.update(updateEntity, wrapper);
    }

    /**
     * 根据参数获取正常状态(normal)的员工绑定列表，使用参数对象封装查询条件
     *
     * @param params 查询参数对象，包含各种过滤条件
     * @return 符合条件的员工绑定列表
     */
    public List<OuterOaEmployeeBindEntity> getNormalStatusEmployeesByParams(OuterOaEmployeeBindParams params) {
        if (params == null) {
            params = new OuterOaEmployeeBindParams();
        }

        // 强制设置绑定状态为normal
        params.setBindStatus(BindStatusEnum.normal);

        // 使用现有的getEntities方法查询数据
        return getEntities(params);
    }

    /**
     * 根据条件批量更新员工绑定状态，并返回受影响的记录 该方法先查询符合条件的记录，然后执行更新操作，最后返回更新后的记录
     *
     * @param params           查询参数
     * @param targetBindStatus 目标绑定状态
     * @return 更新后的员工绑定记录列表
     */
    public List<OuterOaEmployeeBindEntity> updateBindStatusAndReturnRecords(OuterOaEmployeeBindParams params,
            BindStatusEnum targetBindStatus) {
        if (params == null || targetBindStatus == null) {
            return Collections.emptyList();
        }

        // 先查询符合条件的记录
        List<OuterOaEmployeeBindEntity> entities = getEntities(params);
        if (CollectionUtils.isEmpty(entities)) {
            return Collections.emptyList();
        }

        // 提取所有ID
        List<String> ids = entities.stream().map(OuterOaEmployeeBindEntity::getId).filter(StringUtils::isNotEmpty)
                .collect(java.util.stream.Collectors.toList());

        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        // 批量更新状态
        LambdaUpdateWrapper<OuterOaEmployeeBindEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.in(OuterOaEmployeeBindEntity::getId, ids);

        OuterOaEmployeeBindEntity updateEntity = new OuterOaEmployeeBindEntity();
        updateEntity.setBindStatus(targetBindStatus);
        updateEntity.setUpdateTime(System.currentTimeMillis());

        int count = outerOaEmployeeBindMapper.update(updateEntity, wrapper);

        // 如果成功更新，返回更新后的记录
        if (count > 0) {
            // 更新本地实体状态
            entities.forEach(entity -> {
                entity.setBindStatus(targetBindStatus);
                entity.setUpdateTime(updateEntity.getUpdateTime());
            });
            return entities;
        }

        return Collections.emptyList();
    }

    /**
     * 根据ID列表批量更新员工绑定状态，并返回更新后的记录
     *
     * @param ids              需要更新的记录ID列表
     * @param targetBindStatus 目标绑定状态
     * @return 更新后的员工绑定记录列表
     */
    public Integer updateBindStatusByIds(List<String> ids, BindStatusEnum targetBindStatus) {
        if (CollectionUtils.isEmpty(ids) || targetBindStatus == null) {
            return 0;
        }

        // 批量更新状态
        LambdaUpdateWrapper<OuterOaEmployeeBindEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.in(OuterOaEmployeeBindEntity::getId, ids);

        // 设置更新内容
        OuterOaEmployeeBindEntity updateEntity = new OuterOaEmployeeBindEntity();
        updateEntity.setBindStatus(targetBindStatus);
        updateEntity.setUpdateTime(System.currentTimeMillis());

        // 执行更新操作
        int count = outerOaEmployeeBindMapper.update(updateEntity, wrapper);

        return count;
    }

    /*
     * 根据数据中心id,查询数据
     */
    public OuterOaEmployeeBindEntity getEntitiesByDcId(String dcId, String fsEmpId, String outEmpId) {
        LambdaQueryWrapper<OuterOaEmployeeBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaEmployeeBindEntity::getDcId, dcId);
        if(StringUtils.isNotEmpty(fsEmpId)){
            wrapper.eq(OuterOaEmployeeBindEntity::getFsEmpId, fsEmpId);
        }
        if(StringUtils.isNotEmpty(outEmpId)){
            wrapper.eq(OuterOaEmployeeBindEntity::getOutEmpId, outEmpId);
        }


        return outerOaEmployeeBindMapper.selectOne(wrapper);
    }

    /*
     * 根据数据中心id,查询数据
     */
    public List<OuterOaEmployeeBindEntity> getEntitiesByDcId(String dcId, List<String> fsEmpId, List<String> outEmpId) {
        LambdaQueryWrapper<OuterOaEmployeeBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaEmployeeBindEntity::getDcId, dcId);
        if (CollectionUtils.isNotEmpty(fsEmpId)) {
            wrapper.in(OuterOaEmployeeBindEntity::getFsEmpId, fsEmpId);
        }
        if (CollectionUtils.isNotEmpty(outEmpId)) {
            wrapper.in(OuterOaEmployeeBindEntity::getOutEmpId, outEmpId);
        }
        return outerOaEmployeeBindMapper.selectList(wrapper);
    }

    /**
     * 根据数据中心，返回CRMuserids
     */
    public List<String> getCrmUserIdsByDcId(String dcId) {
        List<String> crmUserIds = outerOaEmployeeBindMapper.queryCrmUsrIds(dcId);
        return crmUserIds;
    }

    /**
     * 根据数据中心，返回CRMuserids
     */
    public Integer countCrmUserIds(String dcId) {
        Integer counts = outerOaEmployeeBindMapper.countDataByDcId(dcId);
        return counts;
    }

    /**
     * 查询未绑定的员工信息
     * 
     * @param channel  渠道
     * @param outEa    外部企业账号
     * @param fsEa     纷享企业账号
     * @param appId    应用ID
     * @param pageSize 分页大小
     * @param offset   分页偏移量
     * @return 未绑定的员工信息列表
     */
    public List<Map<String, Object>> queryUnboundEmployees(ChannelEnum channel, String outEa, String fsEa, String appId,
            Integer pageSize, Integer offset) {
        return outerOaEmployeeBindMapper.queryUnboundEmployees(channel, outEa, fsEa, appId, pageSize, offset);
    }

    /**
     * 统计未绑定的员工数量
     * 
     * @param channel 渠道
     * @param outEa   外部企业账号
     * @param fsEa    纷享企业账号
     * @param appId   应用ID
     * @return 未绑定的员工数量
     */
    public Integer countUnboundEmployees(String channel, String outEa, String fsEa, String appId) {
        return outerOaEmployeeBindMapper.countUnboundEmployees(channel, outEa, fsEa, appId);
    }
}
