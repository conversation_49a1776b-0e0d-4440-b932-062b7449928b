package com.facishare.open.qywx.web.service;

import com.alibaba.fastjson.JSONObject;
import com.facishare.open.qywx.accountinner.service.QyweixinGatewayInnerService;
import com.facishare.open.qywx.accountinner.service.ToolsService;
import com.facishare.open.qywx.accountsync.result.Result;
import com.facishare.open.qywx.web.arg.FsEaArg;
import com.facishare.open.qywx.web.arg.GetRuleListArg;
import com.facishare.open.qywx.web.model.result.RuleListResult;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.AbstractJUnit4SpringContextTests;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
public class QyweixinGatewayInnerServiceTest extends AbstractJUnit4SpringContextTests {
    @Resource
    private QyweixinGatewayInnerService qyweixinGatewayInnerService;

    @Test
    public void test() throws Exception {

    }
    @Test
    public void getRuleList(){
        FsEaArg<GetRuleListArg> getRuleListArg=new FsEaArg<>();
        getRuleListArg.setFsEa("84883");
        getRuleListArg.setData(new GetRuleListArg());
        getRuleListArg.getData().setLimit(100);
        Result<RuleListResult> result=qyweixinGatewayInnerService.getRuleList(getRuleListArg);
        System.out.println("");
    }
}
