package com.facishare.open.feishu.sync.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.facishare.open.feishu.sync.mapper.AppInfoMapper;
import com.facishare.open.feishu.sync.mapper.EnterpriseBindMapper;
import com.facishare.open.feishu.syncapi.config.ConfigCenter;
import com.facishare.open.feishu.syncapi.entity.*;
import com.facishare.open.feishu.syncapi.enums.BuyTypeEnum;
import com.facishare.open.feishu.syncapi.model.FeiShuConnectParam;
import com.facishare.open.feishu.syncapi.model.externalApprovals.ExternalInstancesDetail;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.data.QueryTenantInfoData;
import com.facishare.open.feishu.syncapi.service.FeishuTenantService;
import com.facishare.open.oa.base.dbproxy.manager.DescManager;
import com.facishare.open.oa.base.dbproxy.pg.entity.*;
import com.facishare.open.oa.base.dbproxy.pg.manager.*;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.outer.oa.connector.common.api.admin.FeiShuConnectorVo;
import com.facishare.open.outer.oa.connector.common.api.enums.*;
import com.facishare.open.outer.oa.connector.common.api.info.EnterpriseExtendModel;
import com.facishare.open.outer.oa.connector.common.api.info.SettingAccountRulesModel;
import com.facishare.open.outer.oa.connector.common.api.object.FeishuEmployeeObject;
import com.facishare.open.outer.oa.connector.common.api.params.FeishuAppConnectParams;
import com.facishare.open.outer.oa.connector.common.api.result.SystemFieldMappingResult;
import com.fxiaoke.api.IdGenerator;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.facishare.open.feishu.syncapi.consts.Constant.AUTO_BIND_FIELD_EMAIL;
import static com.facishare.open.feishu.syncapi.consts.Constant.AUTO_BIND_FIELD_EMPNUM;

// IgnoreI18nFile
@Component
// IgnoreI18nFile
public class ToolsManager {
    @Autowired
    private AppInfoManager appInfoManager;
    @Autowired
    private EnterpriseBindManager enterpriseBindMapper;
    @Autowired
    private OuterOaAppInfoManager outerOaAppInfoManager;
    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Autowired
    private CorpInfoManager corpInfoManager;
    @Autowired
    private EmployeeBindManager employeeBindManager;
    @Autowired
    private DepartmentBindManager departmentBindManager;
    @Autowired
    private OuterOaEmployeeDataManager outerOaEmployeeDataManager;
    @Autowired
    private OuterOaEmployeeBindManager outerOaEmployeeBindManager;
    @Autowired
    private OuterOaDepartmentBindManager outerOaDepartmentBindManager;
    @Autowired
    private ExternalApprovalsTemplateManager externalApprovalsTemplateManager;
    @Autowired
    private OuterOaMessageTemplateManager outerOaMessageTemplateManager;
    @Autowired
    private ExternalTodoInstanceManager externalTodoInstanceManager;
    @Autowired
    private ExternalTodoTaskManager externalTodoTaskManager;
    @Autowired
    private OuterOaMessageBindManager outerOaMessageBindManager;
    @Autowired
    private CalendarEventManager calendarEventManager;
    @Autowired
    private CalendarManager calendarManager;
    @Autowired
    private OuterOaCalendarTemplateManager outerOaCalendarTemplateManager;
    @Autowired
    private OrderInfoManager orderInfoManager;
    @Autowired
    private OuterOaConfigInfoManager outerOaConfigInfoManager;
    @Autowired
    private FeishuOuterOaConnectManager feishuOuterOaConnectManager;
    @Autowired
    private OuterOaOrderInfoManager outerOaOrderInfoManager;
    @Autowired
    private CalendarEventAttendeeManager calendarEventAttendeeManager ;
    @Autowired
    private OuterOaScheduleBindManager outerOaScheduleBindManager;
    @Autowired
    private FeishuTenantService feishuTenantService;


    public void transferRules(String fsEa){
        EnterpriseBindEntity entity = enterpriseBindMapper.getEntity(fsEa);
        LogUtils.info("transfer rules:{}",fsEa);
        List<OuterOaEnterpriseBindEntity> entitiesByOutEa = outerOaEnterpriseBindManager.getEntitiesByOutEa(ChannelEnum.feishu, entity.getOutEa(), null);
        for (OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity : entitiesByOutEa) {
            OuterOaConfigInfoEntity entityByDataCenterId = outerOaConfigInfoManager.getEntityByDataCenterId(OuterOaConfigInfoTypeEnum.SETTING_BIND_RULES, outerOaEnterpriseBindEntity.getId());
            SettingAccountRulesModel settingAccountRulesModel=JSONObject.parseObject(entityByDataCenterId.getConfigInfo(), SettingAccountRulesModel.class);
            if(entity.getBindType()==BindTypeEnum.manual){
                if(settingAccountRulesModel.getSyncTypeEnum().equals(EnterpriseConfigAccountSyncTypeEnum.accountSync)){
                    continue;

                }
                settingAccountRulesModel.setSyncTypeEnum(EnterpriseConfigAccountSyncTypeEnum.accountBind);
                settingAccountRulesModel.setBindTypeEnum(BindTypeEnum.manual);
                if(entity.getAutBind().equals(2)){
                    settingAccountRulesModel.setBindTypeEnum(BindTypeEnum.auto);
                }
               SettingAccountRulesModel.EmployeeLeaveRule employeeLeaveRule = new SettingAccountRulesModel.EmployeeLeaveRule();
                employeeLeaveRule.setUnbind(false);
                employeeLeaveRule.setStopEmp(false);
                settingAccountRulesModel.setEmployeeLeaveRule(employeeLeaveRule);
                SettingAccountRulesModel.EmployeeRangeRemoveRule removeRule = new SettingAccountRulesModel.EmployeeRangeRemoveRule();
                removeRule.setUnbind(false);
                removeRule.setStopEmp(false);
                settingAccountRulesModel.setEmployeeRangeRemoveRule(removeRule);
                entityByDataCenterId.setConfigInfo(JSON.toJSONString(settingAccountRulesModel));
                outerOaConfigInfoManager.updateById(entityByDataCenterId);
            }

        }
    }




    public void transfer(String fsEa) {
        EnterpriseBindEntity entity = enterpriseBindMapper.getEntity(fsEa);

        Boolean isFirstLand=false;
        Boolean isRetainInformation=true;
        if(entity.getExtend()!=null) {
            EnterpriseExtendModel extendModel = JSONObject.parseObject(entity.getExtend(), EnterpriseExtendModel.class);
            if (ObjectUtils.isNotEmpty(extendModel)) {
                isFirstLand = extendModel.getIsFirstLand();
                isRetainInformation = extendModel.getIsRetainInformation();
            }
        }
        FeishuAppConnectParams feishuAppConnectParams = new FeishuAppConnectParams();
        if (entity.getConnectParams() != null) {
            feishuAppConnectParams = JSONObject.parseObject(entity.getConnectParams(), FeishuAppConnectParams.class);
        }
        // 拿到应用信息
        AppInfoEntity appInfoEntity = appInfoManager.getEntity(entity.getOutEa());
        if(appInfoEntity==null){
            appInfoEntity=new AppInfoEntity();
            appInfoEntity.setAppId(feishuAppConnectParams.getAppId());
        }
        CorpInfoEntity corpInfoEntity = corpInfoManager.getEntityByTenantKey(entity.getOutEa());
        if(corpInfoEntity==null){
            Result<QueryTenantInfoData> queryTenantInfoDataResult = feishuTenantService.initQueryTenantInfo(feishuAppConnectParams.getAppId(), feishuAppConnectParams.getAppSecret(), feishuAppConnectParams.getBaseUrl());
            if(queryTenantInfoDataResult.isSuccess()){
                corpInfoEntity.setTenantName(queryTenantInfoDataResult.getData().getTenant().getName());
                corpInfoEntity.setTenantKey(queryTenantInfoDataResult.getData().getTenant().getTenantKey());
                corpInfoEntity.setDisplayId(queryTenantInfoDataResult.getData().getTenant().getDisplayId());
            }
        }


        OuterOaAppInfoTypeEnum outerOaAppInfoTypeEnum = OuterOaAppInfoTypeEnum.isv;
        if (!ConfigCenter.feishuCrmAppId.equals(appInfoEntity.getAppId())) {
            outerOaAppInfoTypeEnum = OuterOaAppInfoTypeEnum.selfBuild;
        }
        OuterOaAppInfoEntity outerOaAppInfoEntity = OuterOaAppInfoEntity.builder().id(IdGenerator.get())
                .appId(appInfoEntity.getAppId()).outEa(entity.getOutEa()).appType(outerOaAppInfoTypeEnum)
                .status(OuterOaAppInfoStatusEnum.normal).channel(ChannelEnum.feishu).updateTime(new Date().getTime())
                .createTime(new Date().getTime()).appInfo(JSONObject.toJSONString(entity)).build();
        outerOaAppInfoManager.batchUpsertInfos(Lists.newArrayList(outerOaAppInfoEntity));
        LogUtils.info("upsert info data appinfo");
        BindTypeEnum bindType = entity.getBindType();
        FeiShuConnectorVo feiShuConnectorVo = new FeiShuConnectorVo();
        feiShuConnectorVo.setDisplayId(corpInfoEntity.getDisplayId());
        feiShuConnectorVo.setCorpId(corpInfoEntity.getTenantKey());
        feiShuConnectorVo.setEnterpriseName(corpInfoEntity.getTenantName());
        final ChannelEnum channelEnum = ObjectUtils.isNotEmpty(feiShuConnectorVo.getBaseUrl())
                && feishuAppConnectParams.getBaseUrl().equals("larksuite.com") ? ChannelEnum.lark : ChannelEnum.feishu;
        feiShuConnectorVo.setAppId(appInfoEntity.getAppId());
        feiShuConnectorVo.setIsFirstLand(isFirstLand);
        feiShuConnectorVo.setIsRetainInformation(isRetainInformation);
        feiShuConnectorVo.setBaseUrl(feishuAppConnectParams.getBaseUrl());
        feiShuConnectorVo.setAppType(outerOaAppInfoTypeEnum);
        feiShuConnectorVo.setDataCenterName(corpInfoEntity.getTenantName());
        feiShuConnectorVo.setChannel(channelEnum);
        feiShuConnectorVo.setConnectorName("飞书连接器");
        feiShuConnectorVo.setAlertConfig(true);
        if (outerOaAppInfoTypeEnum == OuterOaAppInfoTypeEnum.selfBuild) {
            feiShuConnectorVo.setAppSecret(feishuAppConnectParams.getAppSecret());
            feiShuConnectorVo.setAppId(appInfoEntity.getAppId());
        }
        //看下这个企业有没有灰度日程
        //是否推送
        if(ConfigCenter.CALENDAR_GRAY_EA.contains(fsEa)){
            feiShuConnectorVo.setAlertTypes(Arrays.asList(AlertTypeEnum.CRM_TODO, AlertTypeEnum.CRM_SCHEDULE, AlertTypeEnum.CRM_NOTIFICATION));
        }else{
            feiShuConnectorVo.setAlertTypes(Lists.newArrayList(AlertTypeEnum.CRM_TODO, AlertTypeEnum.CRM_NOTIFICATION));
        }
        String dataCenterId = IdGenerator.get();
        // TODO需要先看下有咩有数据id
        OuterOaEnterpriseBindEntity entity1 = outerOaEnterpriseBindManager.getEntity(channelEnum, entity.getFsEa(), entity.getOutEa(), null);
        if (ObjectUtils.isNotEmpty(entity1)) {
            dataCenterId = entity1.getId();
        }
        OuterOaEnterpriseBindEntity
         outerOaEnterpriseBindEntity=OuterOaEnterpriseBindEntity.builder().
                 outEa(entity.getOutEa()).
         id(dataCenterId).fsEa(entity.getFsEa()).appId(appInfoEntity.getAppId()).bindType(bindType).bindStatus(entity.getBindStatus())
         .channel(ChannelEnum.feishu).connectInfo(JSONObject.toJSONString(feiShuConnectorVo)).createTime(System.currentTimeMillis())
         .updateTime(System.currentTimeMillis())
         .build();
        // Integer countBind =
         outerOaEnterpriseBindManager.batchUpsert(Lists.newArrayList(outerOaEnterpriseBindEntity));

        // 更新人员
        Boolean isPage = true;
        Integer pageNum = 1;  // 从第1页开始，而不是第10页
        Integer pageSize = 20; // 每页获取20条记录，而不是1条
        while (isPage) {
            // 注意这里修正了参数顺序，正确传递pageNum和pageSize
            Page<EmployeeBindEntity> entityListPage = employeeBindManager.getEntityListPage(entity.getFsEa(),
                    entity.getOutEa(), pageNum, pageSize);
            List<EmployeeBindEntity> records = entityListPage.getRecords();
            if (CollectionUtils.isEmpty(records)) {
                isPage = false;
                break;
            }
            List<OuterOaEmployeeBindEntity> outerOaEmployeeBindEntities = Lists.newArrayList();
            for (EmployeeBindEntity employeeBindEntity : records) {
                OuterOaEmployeeBindEntity outerOaEmployeeBindEntity = new OuterOaEmployeeBindEntity();
                outerOaEmployeeBindEntity.setId(IdGenerator.get());
                outerOaEmployeeBindEntity.setDcId(dataCenterId);
                outerOaEmployeeBindEntity.setAppId(appInfoEntity.getAppId());
                outerOaEmployeeBindEntity.setFsEa(entity.getFsEa());
                outerOaEmployeeBindEntity.setOutEa(entity.getOutEa());
                outerOaEmployeeBindEntity.setOutEmpId(employeeBindEntity.getOutUserId());
                outerOaEmployeeBindEntity.setFsEmpId(employeeBindEntity.getFsUserId());
                outerOaEmployeeBindEntity.setBindStatus(employeeBindEntity.getBindStatus());
                outerOaEmployeeBindEntity.setUpdateTime(System.currentTimeMillis());
                outerOaEmployeeBindEntity.setChannel(channelEnum);
                outerOaEmployeeBindEntity.setUpdateTime(System.currentTimeMillis());
                outerOaEmployeeBindEntity.setCreateTime(System.currentTimeMillis());
                outerOaEmployeeBindEntities.add(outerOaEmployeeBindEntity);
            }

            if (!CollectionUtils.isEmpty(outerOaEmployeeBindEntities)) {
                Integer employeeCount = outerOaEmployeeBindManager.batchUpsert(outerOaEmployeeBindEntities);
                LogUtils.info("批量更新员工数据成功，当前页：{}，更新数量：{}", pageNum, employeeCount);
            }

            if (pageNum >= entityListPage.getPages()) {
                isPage = false;
            }
            pageNum++;
        }

        // 读取部门数据
        Boolean isPageDept = true;
        Integer pageDeptNum = 1;  // 从第1页开始
        Integer pageDeptSize = 20;  // 每页获取20条记录
        while (isPageDept) {
            Page<DepartmentBindEntity> departmentBindEntityPage = departmentBindManager
                    .getDepartmentListPage(entity.getFsEa(), entity.getOutEa(), pageDeptNum, pageDeptSize);
            List<DepartmentBindEntity> records = departmentBindEntityPage.getRecords();
            if (CollectionUtils.isEmpty(records)) {
                isPageDept = false;
                break;
            }

            List<OuterOaDepartmentBindEntity> outerOaDepartmentBindEntities = Lists.newArrayList();
            for (DepartmentBindEntity departmentBindEntity : records) {
                OuterOaDepartmentBindEntity outerOaDepartmentBindEntity = OuterOaDepartmentBindEntity.builder()
                        .id(IdGenerator.get()).dcId(dataCenterId).appId(appInfoEntity.getAppId()).fsEa(entity.getFsEa())
                        .outEa(entity.getOutEa()).fsDepId(departmentBindEntity.getFsDepId())
                        .outDepId(departmentBindEntity.getOutDepId()).bindStatus(departmentBindEntity.getBindStatus())
                        .updateTime(System.currentTimeMillis()).createTime(System.currentTimeMillis())
                        .channel(channelEnum).build();
                outerOaDepartmentBindEntities.add(outerOaDepartmentBindEntity);
            }

            if (!CollectionUtils.isEmpty(outerOaDepartmentBindEntities)) {
                Integer deptCount = outerOaDepartmentBindManager.batchUpsert(outerOaDepartmentBindEntities);
                LogUtils.info("批量更新部门数据成功，当前页：{}，更新数量：{}", pageDeptNum, deptCount);
            }

            pageDeptNum++;
            if (pageDeptNum > departmentBindEntityPage.getPages()) {
                isPageDept = false;
            }
        }
        Map<String,String> employees= Maps.newHashMap();
        List<OuterOaEmployeeBindEntity> outerOaEmployeeBindEntities = outerOaEmployeeBindManager.queryByFsEa(outerOaEnterpriseBindEntity.getChannel(), entity.getFsEa(), outerOaEnterpriseBindEntity.getAppId(), BindStatusEnum.normal);
        for (OuterOaEmployeeBindEntity outerOaEmployeeBindEntity : outerOaEmployeeBindEntities) {
            employees.put(outerOaEmployeeBindEntity.getFsEmpId(),outerOaEmployeeBindEntity.getOutEmpId());
        }
        // 消息实例模版
        ExternalApprovalsTemplateEntity externalApprovalsTemplateEntity = externalApprovalsTemplateManager
                .queryEntity(ChannelEnum.feishu, entity.getOutEa());
        if (ObjectUtils.isNotEmpty(externalApprovalsTemplateEntity)) {
            OuterOaMessageTemplateEntity outerOaMessageTemplateEntity = new OuterOaMessageTemplateEntity();
            outerOaMessageTemplateEntity.setChannel(channelEnum);
            outerOaMessageTemplateEntity.setOutEa(entity.getOutEa());
            outerOaMessageTemplateEntity.setTemplateId(externalApprovalsTemplateEntity.getApprovalCode());
            outerOaMessageTemplateEntity.setStatus(OuterOaMessageTemplateStatusEnum.normal);
            outerOaMessageTemplateEntity.setCreateTime(System.currentTimeMillis());
            outerOaMessageTemplateEntity.setUpdateTime(System.currentTimeMillis());
            outerOaMessageTemplateEntity.setId(IdGenerator.get());
            Integer templateCount = outerOaMessageTemplateManager.upsertByChannelAndOutEa(outerOaMessageTemplateEntity);
            LogUtils.info("batch upsert deptCount outerOaMessageTemplateEntity:{}", templateCount);
        }

        // 待办事项处理
        Boolean isPageToDo = true;
        Integer pageToDoNum = 1;  // 从第1页开始
        Integer pageToDoSize = 20;  // 每页获取20条记录
        while (isPageToDo) {
                IPage<ExternalTodoInstanceEntity> todoPage = externalTodoInstanceManager.pageQuery(entity.getFsEa(),
                        pageToDoNum, pageToDoSize);
                List<ExternalTodoInstanceEntity> records = todoPage.getRecords();
                if (CollectionUtils.isEmpty(records)) {
                    isPageToDo = false;
                    break;
                }
                List<OuterOaMessageBindEntity> messageBindEntities = Lists.newArrayList();
                for (ExternalTodoInstanceEntity record : records) {
                    if( record.getTodoDetail()!=null){
                        ExternalInstancesDetail  externalInstancesDetail = JSON.parseObject(record.getTodoDetail(), new TypeReference<ExternalInstancesDetail>(){});
                        for (ExternalInstancesDetail.Task task : externalInstancesDetail.getTaskList()) {
                            OuterOaMessageBindEntity bindEntity = new OuterOaMessageBindEntity();
                            bindEntity.setId(IdGenerator.get());
                            bindEntity.setDcId(dataCenterId);
                            bindEntity.setAppId(appInfoEntity.getAppId());
                            bindEntity.setFsEa(entity.getFsEa());
                            bindEntity.setOutEa(entity.getOutEa());
                            bindEntity.setSourceId(record.getSourceId());
                            bindEntity.setTaskId(record.getInstanceId());
                            bindEntity.setOutUserId(task.getUserId());
                            bindEntity.setMessageInfo(record.getTodoDetail());
                            bindEntity.setEventType(OuterOaMessageBindEventTypeEnum.commonMsg);
                            bindEntity.setMessageType(OuterOaMessageBindMsgTypeEnum.todo);
                            bindEntity.setStatus(record.getStatus() == 0 ? OuterOaMessageBindStatusEnum.pending
                                    : OuterOaMessageBindStatusEnum.approved);
                            bindEntity.setUpdateTime(System.currentTimeMillis());
                            bindEntity.setCreateTime(System.currentTimeMillis());
                            bindEntity.setTemplateId(
                                    externalApprovalsTemplateEntity != null ? externalApprovalsTemplateEntity.getApprovalCode()
                                            : null);
                            bindEntity.setChannel(channelEnum);
                            messageBindEntities.add(bindEntity);
                        }
                    }
                }

                if (!CollectionUtils.isEmpty(messageBindEntities)) {
                    Integer count = outerOaMessageBindManager.batchUpsertInfos(messageBindEntities);
                    LogUtils.info("批量更新待办事项成功，当前页：{}，更新数量：{}", pageToDoNum, count);
                }

                pageToDoNum++;
                if (pageToDoNum > todoPage.getPages()) {
                    isPageToDo = false;
                }
        }
        
        // 机器人
        Boolean todoTaskPage = true;
        Integer todoPageNum = 1;  // 从第1页开始
        Integer todoPageSize = 20;  // 每页获取20条记录
        while (todoTaskPage) {
            Page<ExternalTodoTaskEntity> externalTodoTaskEntityPage = externalTodoTaskManager
                    .queryEntitiesPage(todoPageNum, todoPageSize, entity.getFsEa());
            if (ObjectUtils.isEmpty(externalTodoTaskEntityPage.getRecords())) {
                todoTaskPage = false;
                break;
            }
            List<OuterOaMessageBindEntity> outerOaMessageBindEntities = Lists.newArrayList();
            for (ExternalTodoTaskEntity record : externalTodoTaskEntityPage.getRecords()) {
                OuterOaMessageBindEntity outerOaMessageBindEntity = new OuterOaMessageBindEntity();
                outerOaMessageBindEntity.setId(IdGenerator.get());
                outerOaMessageBindEntity.setDcId(dataCenterId);
                outerOaMessageBindEntity.setAppId(appInfoEntity.getAppId());
                outerOaMessageBindEntity.setFsEa(entity.getFsEa());
                outerOaMessageBindEntity.setOutEa(entity.getOutEa());
                outerOaMessageBindEntity.setSourceId(record.getSourceId());
                outerOaMessageBindEntity.setTaskId(record.getTaskId());
                outerOaMessageBindEntity.setOutUserId(record.getOutUserId());
                outerOaMessageBindEntity.setChannel(channelEnum);
                outerOaMessageBindEntity.setMessageInfo(record.getTaskId());
                outerOaMessageBindEntity.setEventType(OuterOaMessageBindEventTypeEnum.commonMsg);
                outerOaMessageBindEntity.setMessageType(OuterOaMessageBindMsgTypeEnum.bot);
                // 是否完成 0:未完成，1:已完成 2 删除
                if (record.getStatus().equals(0)) {
                    outerOaMessageBindEntity.setStatus(OuterOaMessageBindStatusEnum.pending);
                } else {
                    outerOaMessageBindEntity.setStatus(OuterOaMessageBindStatusEnum.approved);
                }
                outerOaMessageBindEntity.setUpdateTime(System.currentTimeMillis());
                outerOaMessageBindEntity.setCreateTime(System.currentTimeMillis());
                outerOaMessageBindEntities.add(outerOaMessageBindEntity);
            }
            todoPageNum++;
            if (todoPageNum > externalTodoTaskEntityPage.getPages()) {
                todoTaskPage = false;
            }
            Integer todoTaskCount = outerOaMessageBindManager.batchUpsertInfos(outerOaMessageBindEntities);
            LogUtils.info("批量更新机器人待办事项成功，当前页：{}，更新数量：{}", todoPageNum - 1, todoTaskCount);
        }

        // tb_calendar_info
        CalendarEntity calendarEntity = calendarManager.queryEntity(ChannelEnum.feishu, entity.getOutEa());
        if(ObjectUtils.isEmpty(calendarEntity)){
            calendarEntity = calendarManager.queryEntity(ChannelEnum.lark, entity.getOutEa());
        }
        if (ObjectUtils.isNotEmpty(calendarEntity)) {
            OuterOaCalendarTemplateEntity outerOaCalendarTemplateEntity = new OuterOaCalendarTemplateEntity();
            outerOaCalendarTemplateEntity.setId(IdGenerator.get());
            outerOaCalendarTemplateEntity.setChannel(channelEnum);
            outerOaCalendarTemplateEntity.setOutEa(entity.getOutEa());
            outerOaCalendarTemplateEntity.setOutUserId("");
            outerOaCalendarTemplateEntity.setCalendarId(calendarEntity.getCalendarId());
            outerOaCalendarTemplateEntity.setStatus(OuterOaCalendarTemplateStatusEnum.normal);
            outerOaCalendarTemplateEntity.setCreateTime(System.currentTimeMillis());
            outerOaCalendarTemplateEntity.setUpdateTime(System.currentTimeMillis());
            outerOaCalendarTemplateManager.upsert(outerOaCalendarTemplateEntity);
        }
        // tb_calendar_event_info?
        // 机器人

        Boolean calendarTaskPage = true;
        Integer calendarPageNum = 1;  // 从第1页开始
        Integer calendarPageSize = 20;  // 每页获取20条记录
        while (calendarTaskPage){
            Page<CalendarEventEntity> calendarEventEntityPage = calendarEventManager.queryPage(fsEa, calendarPageNum, calendarPageSize);
            if (ObjectUtils.isEmpty(calendarEventEntityPage.getRecords())) {
                calendarTaskPage = false;
                break;
            }
            for (CalendarEventEntity record : calendarEventEntityPage.getRecords()) {
               //需要根据eventid匹配数据
                List<CalendarEventAttendeeEntity> calendarEventAttendeeEntities = calendarEventAttendeeManager.queryEntities(ChannelEnum.feishu, entity.getFsEa(), entity.getOutEa(), record.getEventId());
                List<OuterOaScheduleBindEntity> outerOaScheduleBindEntities = Lists.newArrayList();
                if(CollectionUtils.isNotEmpty(calendarEventAttendeeEntities)){
                    for (CalendarEventAttendeeEntity calendarEventAttendeeEntity : calendarEventAttendeeEntities) {
                        OuterOaScheduleBindEntity outerOaScheduleBindEntity = OuterOaScheduleBindEntity.builder()
                                .id(IdGenerator.get()).dcId(dataCenterId).appId(appInfoEntity.getAppId())
                                .fsEa(entity.getFsEa()).outEa(entity.getOutEa()).fsScheduleId(record.getObjectId())
                                .outScheduleId(calendarEventAttendeeEntity.getEventId())
                                .channel(ChannelEnum.feishu)
                                .createTime(System.currentTimeMillis())
                                .updateTime(System.currentTimeMillis())
                                .status(OuterOaScheduleBindStatusEnum.normal)
                                .outUserId(employees.get(calendarEventAttendeeEntity.getFsUserId())).build();
                        outerOaScheduleBindEntities.add(outerOaScheduleBindEntity);
                    }
                    Integer scheduleCOunt = outerOaScheduleBindManager.batchUpsert(outerOaScheduleBindEntities);
                    LogUtils.info("批量更新日历事件成功，当前页：{}，更新数量：{}", calendarPageNum, scheduleCOunt);
                }
            }
            calendarPageNum++;
            if (calendarPageNum > calendarEventEntityPage.getPages()) {
                calendarTaskPage = false;
            }
        }

        // 订单处理
            List<OrderInfoEntity> allOrdersByCorpId = orderInfoManager.getAllOrdersByCorpId(entity.getOutEa());
            if (!CollectionUtils.isEmpty(allOrdersByCorpId)) {
                LogUtils.info("开始处理企业订单数据，企业ID：{}，订单数量：{}", entity.getOutEa(), allOrdersByCorpId.size());

                for (OrderInfoEntity orderInfoEntity : allOrdersByCorpId) {
                    OuterOaOrderInfoEntity outerOaOrderInfoEntity = OuterOaOrderInfoEntity.builder()
                            .id(IdGenerator.get()).channel(channelEnum).paidOutEa(orderInfoEntity.getPaidCorpId())
                            .orderId(orderInfoEntity.getOrderId()).appId(appInfoEntity.getAppId())
                            .createTime(System.currentTimeMillis())
                            .updateTime(System.currentTimeMillis())
                            .orderType(convertBuyType(orderInfoEntity.getBuyType()))
                            .beginTime(orderInfoEntity.getBeginTime().getTime())
                            .endTime(orderInfoEntity.getEndTime().getTime()).build();
                    // 保存订单信息
                    outerOaOrderInfoManager.insertOrUpdateOrderInfo(outerOaOrderInfoEntity);
                    LogUtils.info("处理订单成功，订单ID：{}", orderInfoEntity.getOrderId());
                }
            }

        // 配置默认规则
        OuterOaConfigInfoEntity entityByAutoFieldDeFault = outerOaConfigInfoManager
                .getEntityByDataCenterId(OuterOaConfigInfoTypeEnum.SETTING_BIND_RULES, dataCenterId);
        OuterOaConfigInfoEntity employeeUniqueIdentity = outerOaConfigInfoManager.getEntityByDataCenterId(
                OuterOaConfigInfoTypeEnum.EMPLOYEE_UNIQUE_IDENTITY, dataCenterId);
        // 自动绑定字段

        String autoField = null;
        if(entity.getAutBind().equals(2)){
           autoField= feishuAppConnectParams.getAutoField();
        }
        boolean contains = ConfigCenter.AUTO_BIND_ACCOUNT_BY_NAME_EA.contains(fsEa);
        if(contains){
            autoField="name";
        }
        if(autoField!=null){
            SettingAccountRulesModel settingAccountRulesModel=JSONObject.parseObject(entityByAutoFieldDeFault.getConfigInfo(),SettingAccountRulesModel.class);
            settingAccountRulesModel.setBindTypeEnum(BindTypeEnum.auto);
            settingAccountRulesModel.setSyncTypeEnum(EnterpriseConfigAccountSyncTypeEnum.accountBind);
            entityByAutoFieldDeFault.setConfigInfo(JSON.toJSONString(settingAccountRulesModel));
            outerOaConfigInfoManager.updateById(entityByAutoFieldDeFault);
            //需要设置规则
            if(autoField.equals(AUTO_BIND_FIELD_EMPNUM)){
                //需要处理工号映射
                SystemFieldMappingResult systemFieldResult = JSONObject.parseObject(employeeUniqueIdentity.getConfigInfo(),
                        SystemFieldMappingResult.class);
                SystemFieldMappingResult.ItemFieldMapping itemFieldMapping=new SystemFieldMappingResult.ItemFieldMapping();
                itemFieldMapping.setCrmFieldLabel("员工编号");
                itemFieldMapping.setCrmFieldApiName(CRMEmployeeFiledEnum.EMPLOYEE_NUMBER.getCode());
                itemFieldMapping.setOuterOAFieldApiName("employee_no");
                itemFieldMapping.setOuterOAFieldLabel("工号");
                itemFieldMapping.setMatchUnique(true);
                systemFieldResult.getItemFieldMappings().add(itemFieldMapping);
                for (int i = 0; i < systemFieldResult.getItemFieldMappings().size(); i++) {
                    Integer index=i+1;
                    SystemFieldMappingResult.ItemFieldMapping fieldMapping = systemFieldResult.getItemFieldMappings().get(i);
                    if(itemFieldMapping.getCrmFieldApiName().equals(CRMEmployeeFiledEnum.EMPLOYEE_NUMBER.name())){
                        fieldMapping.setMatchUnique(true);
                        fieldMapping.setOuterDataText("text"+index);
                    }
                }
                employeeUniqueIdentity.setConfigInfo(JSONObject.toJSONString(systemFieldResult));
            }else{
                if(autoField.equals(AUTO_BIND_FIELD_EMAIL)){
                    //需要处理工号映射
                    SystemFieldMappingResult systemFieldResult = JSONObject.parseObject(employeeUniqueIdentity.getConfigInfo(),
                            SystemFieldMappingResult.class);
                    SystemFieldMappingResult.ItemFieldMapping itemFieldMapping=new SystemFieldMappingResult.ItemFieldMapping();
                    itemFieldMapping.setCrmFieldLabel("邮箱");
                    itemFieldMapping.setCrmFieldApiName(CRMEmployeeFiledEnum.EMAIL.getCode());
                    itemFieldMapping.setOuterOAFieldApiName("email");
                    itemFieldMapping.setOuterOAFieldLabel("邮箱");
                    itemFieldMapping.setMatchUnique(true);
                    systemFieldResult.getItemFieldMappings().add(itemFieldMapping);
                    for (int i = 0; i < systemFieldResult.getItemFieldMappings().size(); i++) {
                        Integer index=i+1;
                        SystemFieldMappingResult.ItemFieldMapping fieldMapping = systemFieldResult.getItemFieldMappings().get(i);
                        if(itemFieldMapping.getCrmFieldApiName().equals(CRMEmployeeFiledEnum.EMAIL.name())){
                            fieldMapping.setMatchUnique(true);
                            fieldMapping.setOuterDataText("text"+index);
                        }
                    }
                    employeeUniqueIdentity.setConfigInfo(JSONObject.toJSONString(systemFieldResult));
                }
                if(autoField.equals("name")){
                    //需要处理工号映射
                    SystemFieldMappingResult systemFieldResult = JSONObject.parseObject(employeeUniqueIdentity.getConfigInfo(),
                            SystemFieldMappingResult.class);
                    SystemFieldMappingResult.ItemFieldMapping itemFieldMapping=new SystemFieldMappingResult.ItemFieldMapping();
                    itemFieldMapping.setCrmFieldLabel("系统名（昵称）");
                    itemFieldMapping.setCrmFieldApiName(CRMEmployeeFiledEnum.NAME.getCode());
                    itemFieldMapping.setOuterOAFieldApiName("name");
                    itemFieldMapping.setOuterOAFieldLabel("用户名");
                    itemFieldMapping.setMatchUnique(true);
                    systemFieldResult.getItemFieldMappings().add(itemFieldMapping);
                    for (int i = 0; i < systemFieldResult.getItemFieldMappings().size(); i++) {
                        Integer index=i+1;
                        SystemFieldMappingResult.ItemFieldMapping fieldMapping = systemFieldResult.getItemFieldMappings().get(i);
                        if(itemFieldMapping.getCrmFieldApiName().equals(CRMEmployeeFiledEnum.NAME.name())){
                            fieldMapping.setMatchUnique(true);
                            fieldMapping.setOuterDataText("text"+index);
                        }
                    }
                    employeeUniqueIdentity.setConfigInfo(JSONObject.toJSONString(systemFieldResult));
                }
            }
            outerOaConfigInfoManager.updateById(employeeUniqueIdentity);
        }else{
             if(entity.getBindType().equals(BindTypeEnum.auto)){
                 SettingAccountRulesModel settingAccountRulesModel=JSONObject.parseObject(entityByAutoFieldDeFault.getConfigInfo(),SettingAccountRulesModel.class);
                 settingAccountRulesModel.setBindTypeEnum(BindTypeEnum.auto);
                 settingAccountRulesModel.setSyncTypeEnum(EnterpriseConfigAccountSyncTypeEnum.accountSync);
                 entityByAutoFieldDeFault.setConfigInfo(JSON.toJSONString(settingAccountRulesModel));
                 outerOaConfigInfoManager.updateById(entityByAutoFieldDeFault);
             }
        }

        OuterOaConfigInfoEntity objectFields = outerOaConfigInfoManager.getEntityByDataCenterId(
                OuterOaConfigInfoTypeEnum.OUTER_SYSTEM_OBJECT_FIELDS, dataCenterId);
        // 重新获取全部数据
        feishuOuterOaConnectManager.refreshOuterEmpData(dataCenterId, channelEnum);
    }

    /**
     * 转换购买类型
     */
    private OuterOaOrderInfoTypeEnum convertBuyType(BuyTypeEnum buyType) {
        if (buyType == null) {
            return OuterOaOrderInfoTypeEnum.buy;
        }
        switch (buyType) {
        case buy:
            return OuterOaOrderInfoTypeEnum.buy;
        case upgrade:
            return OuterOaOrderInfoTypeEnum.upgrade;
        case renew:
            return OuterOaOrderInfoTypeEnum.renew;
        default:
            return OuterOaOrderInfoTypeEnum.buy;
        }
    }

    /**
     * 保存订单信息
     */
    private void saveOrderInfo(OuterOaOrderInfoEntity orderInfo) {
        if (orderInfo == null) {
            return;
        }
        try {
            // TODO: 实现订单保存逻辑
            LogUtils.info("保存订单信息成功，订单ID：{}", orderInfo.getOrderId());
        } catch (Exception e) {
            LogUtils.error("保存订单信息失败，订单ID：{}，错误：{}", orderInfo.getOrderId(), e.getMessage(), e);
        }
    }
}
