package com.facishare.open.ding.template.login;

import com.facishare.open.ding.template.model.Code2UserInfoData;
import com.facishare.open.ding.template.model.FsUserModel;
import com.facishare.open.ding.template.model.LoginAuthModel;
import com.facishare.open.outer.oa.connector.common.api.login.UserTicketModel;
import com.facishare.open.outer.oa.connector.common.api.result.Result;

public interface LoginService {
    /**
     * 飞书免登录code换飞书用户身份
     * @param loginAuthModel
     * @return
     */
    Result<Code2UserInfoData> code2UserInfo(LoginAuthModel loginAuthModel);

    /**
     * 生成纷享ticket
     * @param userTicketModel
     * @return
     */
    Result<String> genFsTicket(UserTicketModel userTicketModel);

    /**
     * 根据ticket获取纷享用户信息
     * @param ticket
     * @return
     */
    Result<FsUserModel> getFsUser(String ticket);
}