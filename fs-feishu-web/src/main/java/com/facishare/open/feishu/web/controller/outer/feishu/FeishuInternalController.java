package com.facishare.open.feishu.web.controller.outer.feishu;

import com.alibaba.fastjson.JSONObject;
import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.feishu.sync.manager.EmployeeBindManager;
import com.facishare.open.feishu.syncapi.config.ConfigCenter;
import com.facishare.open.feishu.syncapi.consts.OutUrlConsts;
import com.facishare.open.feishu.syncapi.entity.EmployeeBindEntity;
import com.facishare.open.feishu.syncapi.entity.EnterpriseBindEntity;
import com.facishare.open.feishu.syncapi.model.EnterpriseModel;
import com.facishare.open.feishu.syncapi.model.jsapi.JsApiSignatureModel;
import com.facishare.open.feishu.syncapi.model.login.FsUserModel;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.ResultCodeEnum;
import com.facishare.open.feishu.syncapi.service.EnterpriseBindService;
import com.facishare.open.feishu.web.enums.UserContextSingleton;
import com.facishare.open.feishu.web.template.inner.jsapi.FeishuJsApiTemplate;
import com.facishare.open.feishu.web.template.inner.login.FeishuLoginTemplate;
import com.facishare.open.feishu.web.template.model.GenFsTicketModel;
import com.facishare.open.feishu.web.template.model.JsApiModel;
import com.facishare.open.feishu.web.utils.ParamCryptoUtil;
import com.facishare.open.feishu.web.utils.SecurityUtil;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEmployeeBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaConfigInfoManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEmployeeBindManager;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.outer.oa.connector.common.api.login.UserTicketModel;
import com.facishare.uc.api.model.enterprise.arg.GetEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseDataResult;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 飞书内部服务接口
 * <AUTHOR>
 * @date 20220726
 */
@RestController
@RequestMapping(value="/feishu/internal")
public class FeishuInternalController {
    @Resource
    private FeishuLoginTemplate feishuLoginTemplate;
    @Resource
    private EnterpriseBindService enterpriseBindService;

    @ReloadableProperty("feishu_redirect_uri")
    private String feishuRedirectUri;//{"cli_a20192f6afb8d00c":"/hcrm/feishu/login?ticket="}

    @ReloadableProperty("crm_domain")
    private String crmDomain;

    @Resource
    private EnterpriseEditionService enterpriseEditionService;

    @Resource
    private FeishuJsApiTemplate feishuJsApiTemplate;
    @Autowired
    private OuterOaConfigInfoManager outerOaConfigInfoManager;

    private static final String VER = "V1_";
    @Autowired
    private OuterOaEmployeeBindManager outerOaEmployeeBindManager;

    /**
     * 用ticket获取纷享用户信息
     * @param ticket
     * @return
     */
    @Deprecated
    @RequestMapping(value="/getFsUserInfo",method = RequestMethod.GET)
    @ResponseBody
    public Result<FsUserModel> getFsUserInfo(@RequestParam String ticket) {
        LogUtils.info("FeishuInternalController.getFsUserInfo,ticket={}",ticket);
        MethodContext context = MethodContext.newInstance(ticket);
        feishuLoginTemplate.getFsUserInfoByTicket(context);
        Result<FsUserModel> fsUser = context.getResultData();
        LogUtils.info("FeishuInternalController.getFsUserInfo,fsUser={}",fsUser);
        return fsUser;
    }

    /**
     * 获取飞书签名
     * 1、前端调用，后面需要屏蔽
     * 2、俊文服务调用
     * @param appId
     * @param url
     * @return
     */
    @Deprecated
    @RequestMapping(value="/getJsApiSignature",method = RequestMethod.GET)
    @ResponseBody
    public Result<JsApiSignatureModel> getJsApiSignature(@RequestParam String appId,
                                                         @RequestParam String url,
                                                         @RequestParam(required = false) String fsEa,
                                                         @RequestParam(required = false) String outEa) {
        //前端调用签名接口需要迁移到web端，为防止还有调用，先兼容
        if(StringUtils.isEmpty(fsEa)) {
            if(ObjectUtils.isEmpty(UserContextSingleton.INSTANCE.getUserContext())) {
                return Result.newError(ResultCodeEnum.IDENTITY_CHECK_ERROR);
            }
            fsEa = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
            LogUtils.info("FeishuInternalController.getJsApiSignature,used,fsEa={}", fsEa);
        }
        if(StringUtils.isAnyEmpty(appId, fsEa, url)) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }

        LogUtils.info("FeishuInternalController.getJsApiSignature,appId={},fsEa={},outEa={},url={}",appId,fsEa,outEa,url);

        JsApiModel jsApiModel = new JsApiModel();
        jsApiModel.setAppId(appId);
        jsApiModel.setFsEa(fsEa);
        jsApiModel.setOutEa(outEa);
        jsApiModel.setUrl(url);

        MethodContext context = MethodContext.newInstance(jsApiModel);
        feishuJsApiTemplate.getJsApiSignature(context);
        Result<JsApiSignatureModel> result = context.getResultData();

        LogUtils.info("FeishuInternalController.getJsApiSignature,result={}",result);

        return result;
    }

    /**
     * 获取和飞书绑定的CRM企业EA列表
     * @param outEa 取飞书的tenantKey
     * @return
     */
    @RequestMapping(value="/getFsEaList",method = RequestMethod.GET)
    @ResponseBody
    public Result<List<EnterpriseModel>> getFsEaList(@RequestParam String outEa, @RequestParam String outUserId) throws Exception {
        outEa = ParamCryptoUtil.decrypt(outEa);
        if(StringUtils.isEmpty(outEa)) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        outUserId = ParamCryptoUtil.decrypt(outUserId);
        if(StringUtils.isEmpty(outUserId)) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        Result<List<EnterpriseModel>> result = enterpriseBindService.getFsEaList(outEa, outUserId);
        if(result.isSuccess() && CollectionUtils.isNotEmpty(result.getData())) {
            for(EnterpriseModel model : result.getData()) {
                model.setEa(ParamCryptoUtil.encrypt(model.getEa()));
            }
        }
        return result;
    }

    /**
     * CRM登录授权，前端在选择CRM企业列表页面使用
     * @param outEa
     * @param appId
     * @param userId
     * @param fsEa 纷享ea，用于一个飞书企业对多个CRM的场景
     * @param response
     * @throws IOException
     */
    @RequestMapping(value="/loginByFsEa",method = RequestMethod.GET)
    @ResponseBody
    public Result<String> loginByFsEa(@RequestParam String outEa,
                                      @RequestParam String appId,
                                      @RequestParam String userId,
                                      @RequestParam String fsEa,
                                      HttpServletResponse response,
                                      HttpServletRequest request) throws Exception {
        LogUtils.info("FeishuInternalController.loginByFsEa,outEa={},appId={},userId={},fsEa={}",outEa,appId,userId,fsEa);
        outEa = ParamCryptoUtil.decrypt(outEa);
        if(StringUtils.isEmpty(outEa)) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        appId =ParamCryptoUtil.decrypt(appId);
        if(StringUtils.isEmpty(appId)) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        userId = ParamCryptoUtil.decrypt(userId);
        if(StringUtils.isEmpty(userId)) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        fsEa = ParamCryptoUtil.decrypt(fsEa);
        if(StringUtils.isEmpty(fsEa)) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        //先判断选中的企业位于什么环境
//        Result<OuterOaEnterpriseBindEntity> enterpriseBindEntityResult = enterpriseBindService.getEnterpriseBind(outEa, fsEa);
//        OuterOaEnterpriseBindEntity enterpriseBindEntity = enterpriseBindEntityResult.getData();
//        String domain = enterpriseBindEntity.getDomain();
//        if(!domain.equals(crmDomain)) {
//            //转到对应的环境处理
//            String redirectUri = domain + "/erpdss/feishu/external/loginCloudAuth?channel=feishu&outEa="+ URLEncoder.encode(SecurityUtil.encryptStr(VER+outEa), "utf-8")+"&appId="+URLEncoder.encode(SecurityUtil.encryptStr(VER+appId), "utf-8")+"&userId="+URLEncoder.encode(SecurityUtil.encryptStr(VER+userId), "utf-8")+"&fsEa="+URLEncoder.encode(SecurityUtil.encryptStr(VER+fsEa), "utf-8");
//            return Result.newSuccess(redirectUri);
//        }
        OuterOaEmployeeBindEntity employeeBindEntity = outerOaEmployeeBindManager.getEmployeeBindEntity(null, outEa, fsEa, appId, userId);
        //1.生成CRM ticket

        UserTicketModel userTicketModel=new UserTicketModel(outEa,appId,employeeBindEntity.getFsEmpId(),employeeBindEntity.getOutEmpId(),System.currentTimeMillis(),employeeBindEntity.getFsEa());
        MethodContext context = MethodContext.newInstance(userTicketModel);
        feishuLoginTemplate.genFsTicket(context);
        Result<String> ticket = context.getResultData();
        LogUtils.info("FeishuInternalController.loginByFsEa,ticket={}",ticket);

        //获取纷享企业的信息
//        String doMain = crmDomain;
//        GetEnterpriseDataResult enterpriseInfo = getEnterpriseInfo(fsEa);
//        if(ObjectUtils.isNotEmpty(enterpriseInfo)
//                && ObjectUtils.isNotEmpty(enterpriseInfo.getEnterpriseData())
//                && StringUtils.isNotEmpty(enterpriseInfo.getEnterpriseData().getDomain())) {
//            doMain = enterpriseInfo.getEnterpriseData().getDomain();
//        }
        //2.生成重定向uri
        String redirectUri = getFeishuRedirectUri(appId,fsEa) + ticket.getData();
        LogUtils.info("FeishuInternalController.loginByFsEa,redirectUri={}",redirectUri);

        //3.执行重定向
        //response.sendRedirect(redirectUri);

        return Result.newSuccess(getEnterpriseInfo(fsEa).getEnterpriseData().getDomain()+redirectUri);
    }

    /**
     * 检查并初始化飞书连接器，仅供集成平台调用
     * @param fsEa
     * @return
     */
    @Deprecated
    @RequestMapping(value="/checkAndInitConnector",method = RequestMethod.GET)
    @ResponseBody
    public Result<Void> checkAndInitConnector(@RequestParam String fsEa,@RequestParam(required = false) String channel,
                                              @RequestParam(required = false) String dataCenterId) {
        LogUtils.info("checkAndInitConnector,fsEa={},dataCenterId={}",fsEa,dataCenterId);
        return enterpriseBindService.checkAndInitConnector(fsEa,dataCenterId,channel);
    }

    private GetEnterpriseDataResult getEnterpriseInfo(String fsEa) {
        GetEnterpriseDataArg arg = new GetEnterpriseDataArg();
        arg.setEnterpriseAccount(fsEa);
        GetEnterpriseDataResult result = enterpriseEditionService.getEnterpriseData(arg);
        LogUtils.info("ControllerQYWeixin.getEnterpriseInfo,result={}",result);
        return result;
    }

//    private String decodeData(String data) {
//        Pattern pattern = Pattern.compile(VER+"(.*)");
//        Matcher matcher = pattern.matcher(SecurityUtil.decryptStr(data));
//        if (matcher.find()) {
//            return matcher.group(1);
//        } else {
//            return null;
//        }
//    }
//    private String getFeishuRedirectUri(String appId){
//        JSONObject redirectUriJson = JSONObject.parseObject(feishuRedirectUri);
//        String feishuAppidUrl = redirectUriJson.getString(appId);
//        if(ObjectUtils.isNotEmpty(feishuAppidUrl)){
//            return feishuAppidUrl;
//        }
//        return OutUrlConsts.feishu_crmurl;
//    }
    private String getFeishuRedirectUri(String appId,String fsEa){
        JSONObject redirectUriJson = JSONObject.parseObject(feishuRedirectUri);
        if( outerOaConfigInfoManager.getRouteErpDssLogin(fsEa)){
            redirectUriJson= JSONObject.parseObject(ConfigCenter.feishu_redirect_uri_gray);
        }
        String feishuAppidUrl = redirectUriJson.getString(appId);

        if(ObjectUtils.isNotEmpty(feishuAppidUrl)){
            return feishuAppidUrl;
        }
        return OutUrlConsts.feishu_crmurl;
    }
}
