package com.facishare.open.outer.oa.connector.common.api.enums;

import lombok.Getter;

//IgnoreI18nFile
@Getter
public enum OuterOaConfigInfoTypeEnum {
    EMPLOYEE_UNIQUE_IDENTITY("EMPLOYEE_UNIQUE_IDENTITY", "自动绑定，用于匹配CRM人员"),
    SETTING_BIND_RULES("SETTING_BIND_RULES", "账号绑定设置"),
    OUTER_SYSTEM_OBJECT_FIELDS("OUTER_SYSTEM_OBJECT_FIELDS", "外部系统字段描述"),
    DEPARTMENT_OBJECT_LAYOUT_MAPPING("DEPARTMENT_OBJECT_LAYOUT_MAPPING", "部门对象布局映射"),
    CONTACTS_USER_DEFINED_MOBILE_KEY("CONTACTS_USER_DEFINED_MOBILE_KEY", "通讯录用户自定义手机号码字段key"),
    CONVERSATION_ARCHIVE_CONFIG("CONVERSATION_ARCHIVE_CONFIG", "会话存档配置"),
    INTERFACE_CALL_SERVICE_AUTH("INTERFACE_CALL_SERVICE_AUTH", "接口调用服务权限"),
    DEPARTMENT_OBJECT_FIELDS_MAPPING("DEPARTMENT_OBJECT_FIELDS_MAPPING", "部门字段ID映射"),
    HIT_KEY_WORD("HIT_KEY_WORD", "命中关键词规则"),
    ROUTE_ERPDSS_NORMAL_LOGIN_ENTERPRISE_ACCOUNTS("ROUTE_ERPDSS_NORMAL_LOGIN_ENTERPRISE_ACCOUNTS", "路由统一基座登录页")
    ;

    private final String code;
    private final String desc;

    OuterOaConfigInfoTypeEnum(final String code, final String desc) {
        this.code = code;
        this.desc = desc;
    }
}
