package com.facishare.open.qywx.web.manager;

import com.alibaba.fastjson.JSON;
import com.facishare.open.oa.base.dbproxy.manager.ObjectDataManager;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.order.contacts.proxy.api.arg.FsEmpArg;
import com.facishare.open.order.contacts.proxy.api.enums.FsEmployeeRoleCodeEnum;
import com.facishare.open.order.contacts.proxy.api.result.Result;
import com.facishare.open.order.contacts.proxy.api.service.FsEmployeeServiceProxy;
import com.facishare.open.outer.oa.connector.common.api.admin.QywxConnectorVo;
import com.facishare.open.outer.oa.connector.common.api.enums.BindTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.EmplyeeBindChangeTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaAppInfoTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.result.OuterOAConnectSettingResult;
import com.facishare.open.qywx.web.job.AutoGetUserAndDepartmentInfoHandler;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.result.ActionAddResult;
import com.fxiaoke.crmrestapi.result.IncrementUpdateResult;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.AbstractJUnit4SpringContextTests;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
public class QyweixinOuterOaConnectManagerTest extends AbstractJUnit4SpringContextTests {
    @Resource
    private QyweixinOuterOaConnectManager qyweixinOuterOaConnectManager;
    @Autowired
    private ObjectDataManager objectDataManager;
    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;

    @Test
    public void test() throws Exception {
        String x = "{\"connectParams\":{\"qywx\":{\"appType\":\"serviceRepDev\",\"qywxCorpId\":\"wpwx1mDAAALG1GObCJSt5_5ruswcU9gA\",\"qywxEnterpriseName\":\"灵感菇\",\"qywxDepartmentId\":\"1\",\"agentId\":1000002,\"domain\":\"https://crm.ceshi112.com\",\"isFirstLand\":false,\"isRetainInformation\":false,\"dataCenterId\":\"67f91001c420090001effcca\",\"connectorName\":\"企业微信连接器\",\"dataCenterName\":\"企业微信连接器\",\"channel\":\"qywx\",\"authType\":\"OAUTH2\",\"alertConfig\":true,\"alertTypes\":[\"CRM_TODO\",\"CRM_NOTIFICATION\"]}},\"channelEnum\":\"qywx\",\"bindTypeEnum\":\"auto\",\"outerOaAppInfoTypeEnum\":\"serviceRepDev\",\"currentDcId\":\"67f91001c420090001effccb\"}";
        OuterOAConnectSettingResult result = JSON.parseObject(x, OuterOAConnectSettingResult.class);
        qyweixinOuterOaConnectManager.doValidateConfigAndSave(result, ChannelEnum.qywx, OuterOaAppInfoTypeEnum.serviceRepDev);
    }

    @Test
    public void testEmployeeBindChangeEvent() throws Exception {
        qyweixinOuterOaConnectManager.employeeBindChangeEvent("67f91001c420090001effccb", Lists.newArrayList("67f9e15eb04308bec08e51a7", "67fa0ddaf25b1100018c6fcb"), EmplyeeBindChangeTypeEnum.employee_bind);
    }

    @Test
    public void create() {
        OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity = outerOaEnterpriseBindManager.getEntityById("67f613299c90100001b78ac7");
        com.facishare.open.outer.oa.connector.common.api.result.Result<ActionAddResult> employee = objectDataManager
                .createEmployee(outerOaEnterpriseBindEntity, "wowx1mDAAADfbMpW0R7g0mWrTXvYr2bw");
        System.out.println(employee);
    }
}
