package com.facishare.open.oa.base.dbproxy.mongo.dao;

import com.facishare.open.oa.base.dbproxy.mongo.document.OaConnectorSyncEventDataDoc;
import com.facishare.open.oa.base.dbproxy.mongo.store.OaConnectorSyncEventDataMongoStore;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.params.QueryOaConnectorSyncEventDataArg;
import com.google.common.collect.Lists;
import com.mongodb.MongoException;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.*;
import com.mongodb.client.result.DeleteResult;
import com.mongodb.client.result.UpdateResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.springframework.stereotype.Repository;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Updates.combine;
import static com.mongodb.client.model.Updates.currentDate;
import static com.mongodb.client.model.Updates.set;
import static com.mongodb.client.model.Updates.setOnInsert;

/**
 * oa连接器事件推送操作mongo的dao类封装
 * <AUTHOR>
 * @date 2023/12/06
 */
@Slf4j
@Repository
public class OaConnectorSyncEventDataMongoDao {
    public static final String oa_id = "_id";
    public static final String oa_channel = "channel";
    public static final String oa_outEa = "outEa";
    public static final String oa_appId = "appId";
    public static final String oa_eventType = "eventType";
    public static final String oa_event = "event";
    public static final String oa_status = "status";
    public static final String oa_createTime = "createTime";
    public static final String oa_updateTime = "updateTime";

    private final OaConnectorSyncEventDataMongoStore store;

    public OaConnectorSyncEventDataMongoDao(OaConnectorSyncEventDataMongoStore store) {
        this.store = store;
    }

    private List<ObjectId> convertObjectIds(Collection<String> ids) {
        List<ObjectId> objIds = ids.stream()
                .filter(ObjectId::isValid)
                .map(v -> new ObjectId(v)).collect(Collectors.toList());
        return objIds;
    }

    private static ObjectId getObjId(String id) {
        return new ObjectId(id);
    }

    private int update(List<Bson> filters, List<Bson> updates) {
        updates.add(currentDate(oa_updateTime));
        Bson filter = and(filters);
        Bson update = combine(updates);
        UpdateResult updateResult = store.getOrCreateCollection().updateOne(filter, update);
        return (int) updateResult.getModifiedCount();
    }


    public List<OaConnectorSyncEventDataDoc> listByTenantId(Integer offset, Integer limit) {
        List<OaConnectorSyncEventDataDoc> res = new ArrayList<>();
        store.getOrCreateCollection().find().limit(limit).skip(offset).into(res);
        return res;
    }

    /**
     * 忽略结果
     *
     * @param syncEventDataDoc
     * @return 1 成功 0 失败
     */

    public int insertIgnore(OaConnectorSyncEventDataDoc syncEventDataDoc) {
        MongoCollection<OaConnectorSyncEventDataDoc> collection = store.getOrCreateCollection();
        try {
            collection.insertOne(syncEventDataDoc);
        } catch (MongoException mongoException) {
            log.error("sync data insert exception", mongoException);
            return 0;
        }
        return 1;
    }


    public OaConnectorSyncEventDataDoc getById(@NotNull String id) {
        if (!ObjectId.isValid(id)) {
            return null;
        }
        OaConnectorSyncEventDataDoc syncEventDataDoc = store.getOrCreateCollection().find(eq(new ObjectId(id))).limit(1).first();
        return syncEventDataDoc;
    }


    public OaConnectorSyncEventDataDoc getSimpleById(@NotNull String id) {
        if (!ObjectId.isValid(id)) {
            return null;
        }
        OaConnectorSyncEventDataDoc syncEventDataDoc = store.getOrCreateCollection()
                .find(eq(new ObjectId(id)))
                .limit(1).first();
        return syncEventDataDoc;
    }


    public List<OaConnectorSyncEventDataDoc> listByIds(Collection<String> ids) {
        List<ObjectId> objIds = convertObjectIds(ids);
        if (objIds.isEmpty()) {
            return new ArrayList<>();
        }
        Bson filter = Filters.in(oa_id, objIds);
        List<OaConnectorSyncEventDataDoc> res = new ArrayList<>();
        store.getOrCreateCollection().find(filter).into(res);
        return res;
    }


    public List<OaConnectorSyncEventDataDoc> listSimpleByIds(Collection<String> ids) {
        List<ObjectId> objIds = convertObjectIds(ids);
        if (objIds.isEmpty()) {
            return new ArrayList<>();
        }
        Bson filter = Filters.in(oa_id, objIds);
        List<OaConnectorSyncEventDataDoc> res = new ArrayList<>();
        store.getOrCreateCollection().find(filter).into(res);
        return res;
    }

    public void save(OaConnectorSyncEventDataDoc syncEventDataDoc) {
        log.info("OaConnectorSyncEventDataMongoDao.save,syncEventDataDoc={}", syncEventDataDoc);
        Bson filter = and(eq(oa_id, syncEventDataDoc.getId()));
        store.getOrCreateCollection().replaceOne(filter, syncEventDataDoc, new ReplaceOptions().upsert(true));
    }

    /**
     * 批量插入或替换记录
     *
     * @param docList
     * @return
     */
    public BulkWriteResult batchReplace(Collection<OaConnectorSyncEventDataDoc> docList) {
        log.info("OaConnectorSyncEventDataMongoDao.batchReplace,docList={}", docList);
        List<WriteModel<OaConnectorSyncEventDataDoc>> request = new ArrayList<>();
        for (OaConnectorSyncEventDataDoc doc : docList) {
            Bson filter = and(eq(oa_id, doc.getId()));
            request.add(new ReplaceOneModel<>(filter, doc, new ReplaceOptions().upsert(true)));
        }
        BulkWriteResult bulkWriteResult = store.getOrCreateCollection()
                .bulkWrite(request, new BulkWriteOptions().ordered(false));
        log.info("OaConnectorSyncEventDataMongoDao.batchReplace,bulkWriteResult={}", bulkWriteResult);
        return bulkWriteResult;
    }


    /**
     * 批量更新，无则不插入。
     *
     * @param docList
     * @return insert count
     */
    public BulkWriteResult batchUpdate(Collection<OaConnectorSyncEventDataDoc> docList) {
        log.info("OaConnectorSyncEventDataMongoDao.batchUpdate,docList={}", docList);
        List<WriteModel<OaConnectorSyncEventDataDoc>> request = new ArrayList<>();
        for (OaConnectorSyncEventDataDoc doc : docList) {
            Bson filter = and(eq(oa_id, doc.getId()));
            request.add(new ReplaceOneModel<>(filter, doc, new ReplaceOptions().upsert(true)));
        }
        BulkWriteResult bulkWriteResult = store.getOrCreateCollection()
                .bulkWrite(request, new BulkWriteOptions().ordered(false));
        log.info("OaConnectorSyncEventDataMongoDao.batchUpdate,bulkWriteResult={}", bulkWriteResult);
        return bulkWriteResult;
    }

    public List<OaConnectorSyncEventDataDoc> pageByQuerySyncEventDataArg(QueryOaConnectorSyncEventDataArg arg) {
        List<OaConnectorSyncEventDataDoc> res = new ArrayList<>();
        Bson filter = buildFilterByQuerySyncEventDataArg(arg);
        int offset=(arg.getPageNum()-1) * arg.getPageSize();
        store.getOrCreateCollection().find(filter)
                .skip(offset)
                .limit(arg.getPageSize())
                .into(res);
        return res;
    }

    private Bson buildFilterByQuerySyncEventDataArg(QueryOaConnectorSyncEventDataArg arg) {
        List<Bson> filters = Lists.newArrayList(
                Filters.eq(oa_status, arg.getStatus()));
        if(StringUtils.isNotEmpty(arg.getOutEa())) {
            List<Bson> outEaFilters = Lists.newArrayList(Filters.eq(oa_outEa, arg.getOutEa()));
            filters.add(Filters.and(outEaFilters));
        }
        if(ObjectUtils.isNotEmpty(arg.getChannel())) {
            filters.add(Filters.eq(oa_channel, arg.getChannel().name()));
        }
        if(CollectionUtils.isNotEmpty(arg.getChannelEnums())) {
            List<String> channelEnums = arg.getChannelEnums().stream().map(ChannelEnum::name).collect(Collectors.toList());
            filters.add(Filters.in(oa_channel,channelEnums));
        }
        if(StringUtils.isNotEmpty(arg.getAppId())){

            filters.add(Filters.eq(oa_appId, arg.getAppId()));
        }
        List<Bson> eventTypeAllFilters = new LinkedList<>();
        if(CollectionUtils.isNotEmpty(arg.getEventType())) {
            for(String eventType : arg.getEventType()) {
                List<Bson> eventTypeFilters = Lists.newArrayList(Filters.eq(oa_eventType, eventType));
                Bson filterBson = Filters.and(eventTypeFilters);
                eventTypeAllFilters.add(filterBson);
            }
            filters.add(Filters.or(eventTypeAllFilters));
        }

        return Filters.and(filters);
    }

    public DeleteResult deleteTableData(OaConnectorSyncEventDataDoc doc) {
        Bson filter = and(eq(oa_id, doc.getId()));
        DeleteResult deleteResult = store.getOrCreateCollection().deleteMany(filter);
        log.info("OaConnectorSyncEventDataMongoDao.deleteTableData,deleteResult={}", deleteResult);
        return deleteResult;
    }

    public DeleteResult deleteTableDataByDelArg(QueryOaConnectorSyncEventDataArg arg) {
        Bson filter = buildFilterByQuerySyncEventDataArg(arg);
        DeleteResult deleteResult = store.getOrCreateCollection().deleteMany(filter);
        log.info("OaConnectorSyncEventDataMongoDao.deleteTableDataByDelArg,deleteResult={}", deleteResult);
        return deleteResult;
    }

    public Long countDocuments(ChannelEnum channel, String outEa, String appId) {
        Bson filter = and(eq(oa_outEa, outEa), eq(oa_channel, channel.name()));
        if(StringUtils.isNotEmpty(appId)) {
            filter = and(filter, eq(oa_appId, appId));
        }
        return store.getOrCreateCollection().countDocuments(filter);
    }

    //根据ids删除
    public DeleteResult deleteByIds(Collection<String> ids) {
        List<ObjectId> objectIds = convertObjectIds(ids);
        Bson filter = Filters.in(oa_id, objectIds);
        DeleteResult deleteResult = store.getOrCreateCollection().deleteMany(filter);
        log.info("OaConnectorSyncEventDataMongoDao.deleteByIds,deleteResult={}", deleteResult);
        return deleteResult;
    }

    public BulkWriteResult upsertByOutEaAppIdEventType(OaConnectorSyncEventDataDoc doc) {
        log.info("OaConnectorSyncEventDataMongoDao.upsertByOutEaAppIdEventType,doc={}", doc);
        List<WriteModel<OaConnectorSyncEventDataDoc>> request = new ArrayList<>();

        // 构建过滤条件
        Bson filter = and(
            eq(oa_outEa, doc.getOutEa()),
            eq(oa_appId, doc.getAppId()),
            eq(oa_eventType, doc.getEventType())
        );

        // 构建更新操作 - 使用UpdateOneModel避免_id冲突
        List<Bson> updates = new ArrayList<>();
        updates.add(set(oa_channel, doc.getChannel().name()));
        updates.add(set(oa_outEa, doc.getOutEa()));
        updates.add(set(oa_appId, doc.getAppId()));
        updates.add(set(oa_eventType, doc.getEventType()));
        updates.add(set(oa_event, doc.getEvent()));
        updates.add(set(oa_status, doc.getStatus()));
        updates.add(currentDate(oa_updateTime));

        // 在插入时设置的字段（只在新文档时设置）
        updates.add(setOnInsert(oa_id, doc.getId()));
        updates.add(setOnInsert(oa_createTime, doc.getCreateTime()));

        Bson update = combine(updates);

        // 创建 upsert 请求
        request.add(new UpdateOneModel<>(filter, update, new UpdateOptions().upsert(true)));

        // 执行批量写入
        BulkWriteResult bulkWriteResult = store.getOrCreateCollection()
                .bulkWrite(request, new BulkWriteOptions().ordered(false));
        log.info("OaConnectorSyncEventDataMongoDao.upsertByOutEaAppIdEventType,bulkWriteResult={}", bulkWriteResult);
        return bulkWriteResult;
    }
}
