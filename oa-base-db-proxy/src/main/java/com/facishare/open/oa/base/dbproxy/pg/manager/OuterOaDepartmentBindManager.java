package com.facishare.open.oa.base.dbproxy.pg.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaDepartmentBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.mapper.OuterOaDepartmentBindMapper;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaDepartmentBindParams;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaDepartmentBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.mapper.OuterOaDepartmentBindMapper;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaDepartmentBindParams;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.fxiaoke.api.IdGenerator;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.Objects;
import java.util.Map;

/**
 * 外部OA部门绑定管理类
 */
@Service
@Slf4j
public class OuterOaDepartmentBindManager {

    @Autowired
    private OuterOaDepartmentBindMapper outerOaDepartmentBindMapper;

    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;

    public Integer insert(OuterOaDepartmentBindEntity entity) {
        if (StringUtils.isEmpty(entity.getId())) {
            entity.setId(IdGenerator.get());
        }
        return outerOaDepartmentBindMapper.insert(entity);
    }

    //delete
    public Integer deleteById(OuterOaDepartmentBindEntity entity) {
        return outerOaDepartmentBindMapper.deleteById(entity);
    }

    /**
     * 批量保存或更新部门绑定关系
     *
     * @param entities 部门绑定实体列表
     * @return 影响行数
     */
    public Integer batchUpsert(List<OuterOaDepartmentBindEntity> entities) {
        if (CollectionUtils.isEmpty(entities)) {
            return 0;
        }
        entities.forEach(entity -> {
            if (StringUtils.isEmpty(entity.getId())) {
                entity.setId(IdGenerator.get());
            }
            // 设置时间戳
            long now = System.currentTimeMillis();
            if (entity.getCreateTime() == null) {
                entity.setCreateTime(now);
            }
            entity.setUpdateTime(now);
        });

        return outerOaDepartmentBindMapper.batchUpsert(entities);
    }

    public Integer upsert(OuterOaDepartmentBindEntity entity) {
        if (Objects.isNull(entity)) {
            return 0;
        }
        return batchUpsert(Lists.newArrayList(entity));
    }

    /**
     * 批量插入部门绑定关系
     */
    public Integer batchInsert(List<OuterOaDepartmentBindEntity> entities) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(entities)) {
            return 0;
        }
        entities.forEach(entity -> {
            if (StringUtils.isEmpty(entity.getId())) {
                entity.setId(IdGenerator.get());
            }
        });
        return outerOaDepartmentBindMapper.batchUpsert(entities);
    }

    /**
     * 根据企业账号查询部门绑定关系
     */
    public List<OuterOaDepartmentBindEntity> queryByAppId(ChannelEnum channel, String fsEa, String appId) {
        LambdaQueryWrapper<OuterOaDepartmentBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaDepartmentBindEntity::getChannel, channel)
                .eq(OuterOaDepartmentBindEntity::getFsEa, fsEa)
                .eq(OuterOaDepartmentBindEntity::getAppId, appId)
                .eq(OuterOaDepartmentBindEntity::getBindStatus, BindStatusEnum.normal);
        return outerOaDepartmentBindMapper.selectList(wrapper);
    }

    /**
     * 根据企业账号和外部部门ID查询部门绑定关系
     */
    public OuterOaDepartmentBindEntity queryByOutDepId(ChannelEnum channel, String fsEa, String appId, String outDepId) {
        LambdaQueryWrapper<OuterOaDepartmentBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaDepartmentBindEntity::getChannel, channel)
                .eq(OuterOaDepartmentBindEntity::getFsEa, fsEa)
                .eq(OuterOaDepartmentBindEntity::getOutDepId, outDepId)
                .eq(OuterOaDepartmentBindEntity::getAppId, appId)
                .eq(OuterOaDepartmentBindEntity::getBindStatus, BindStatusEnum.normal);
        return outerOaDepartmentBindMapper.selectOne(wrapper);
    }

    /**
     * 更新部门绑定关系
     */
    public Integer updateById(OuterOaDepartmentBindEntity entity) {
        return outerOaDepartmentBindMapper.updateById(entity);
    }

    // 如果对应的外部员工已有绑定关系，则返回对应的fsEmpId
    public String getFsDeptIdByEaAndOutDeptId(ChannelEnum channel, String fsEa, String outEa, String outDeptId) {
        LambdaQueryWrapper<OuterOaDepartmentBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaDepartmentBindEntity::getChannel, channel);
        wrapper.eq(OuterOaDepartmentBindEntity::getOutEa, outEa);
        wrapper.eq(OuterOaDepartmentBindEntity::getFsEa, fsEa);
        wrapper.eq(OuterOaDepartmentBindEntity::getOutDepId, outDeptId);
        wrapper.eq(OuterOaDepartmentBindEntity::getBindStatus, BindStatusEnum.normal);
        final OuterOaDepartmentBindEntity outerOaDepartmentBindEntity = outerOaDepartmentBindMapper.selectOne(wrapper);
        return null == outerOaDepartmentBindEntity ? null :outerOaDepartmentBindEntity.getFsDepId();
    }

    public OuterOaDepartmentBindEntity getDeptEntityByDcId(String dataCenterId, String fsDeptId, String outDeptId) {
        LambdaQueryWrapper<OuterOaDepartmentBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaDepartmentBindEntity::getDcId, dataCenterId);
       if(StringUtils.isNotBlank(fsDeptId)){
           wrapper.eq(OuterOaDepartmentBindEntity::getFsDepId, fsDeptId);
       }
       if(StringUtils.isNotBlank(outDeptId)){
            wrapper.eq(OuterOaDepartmentBindEntity::getOutDepId, outDeptId);
        }
        wrapper.eq(OuterOaDepartmentBindEntity::getBindStatus, BindStatusEnum.normal);
       OuterOaDepartmentBindEntity outerOaDepartmentBindEntity = outerOaDepartmentBindMapper.selectOne(wrapper);
       return outerOaDepartmentBindEntity;
    }

    public boolean checkFsDeptHasBind(String fsEa, String fsDepId) {
        LambdaQueryWrapper<OuterOaDepartmentBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaDepartmentBindEntity::getFsEa, fsEa);
        wrapper.eq(OuterOaDepartmentBindEntity::getFsDepId, fsDepId);
        wrapper.eq(OuterOaDepartmentBindEntity::getBindStatus, BindStatusEnum.normal);
        return outerOaDepartmentBindMapper.exists(wrapper);
    }

    public String getFsDeptIdAndAddByEaAndOutDeptId(ChannelEnum channel, String fsEa, String appId, String outDeptId) {
        final OuterOaEnterpriseBindEntity entity = outerOaEnterpriseBindManager.getByFsEaAndAppId(channel, fsEa, appId);
        // 检查是否有其他appId已经映射了
        final String fsDeptId = getFsDeptIdByEaAndOutDeptId(ChannelEnum.dingding, entity.getFsEa(), entity.getOutEa(), outDeptId);
        if (StringUtils.isEmpty(fsDeptId)) {
            return null;
        }
        // 写入绑定表
        upsert(new OuterOaDepartmentBindEntity(IdGenerator.get(), ChannelEnum.dingding, entity.getId(), entity.getFsEa(), entity.getOutEa(), entity.getAppId(), fsDeptId, outDeptId, BindStatusEnum.normal, System.currentTimeMillis(), System.currentTimeMillis(), null));
        return fsDeptId;
    }


    public Integer deleteDepartmentBinds(ChannelEnum channel, String fsEa, String outEa, String appId, List<String> outDepIds, List<String> fsDepIds) {
        //删除
        LambdaUpdateWrapper<OuterOaDepartmentBindEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(OuterOaDepartmentBindEntity::getChannel, channel);
        wrapper.eq(OuterOaDepartmentBindEntity::getOutEa, outEa);
        wrapper.eq(OuterOaDepartmentBindEntity::getFsEa, fsEa);
        wrapper.eq(OuterOaDepartmentBindEntity::getAppId, appId);
        //判断
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(outDepIds)) {
            wrapper.in(OuterOaDepartmentBindEntity::getOutDepId, outDepIds);
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(fsDepIds)) {
            wrapper.in(OuterOaDepartmentBindEntity::getFsDepId, fsDepIds);
        }
        return outerOaDepartmentBindMapper.delete(wrapper);
    }


    public OuterOaDepartmentBindEntity queryOuterOaDepartmentBindEntityByOutDepId(ChannelEnum channel, String fsEa, String outEa, String appId, String outDepId) {
        LambdaQueryWrapper<OuterOaDepartmentBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaDepartmentBindEntity::getChannel, channel);
        wrapper.eq(OuterOaDepartmentBindEntity::getOutEa, outEa);
        wrapper.eq(OuterOaDepartmentBindEntity::getFsEa, fsEa);
        wrapper.eq(OuterOaDepartmentBindEntity::getAppId, appId);
        wrapper.eq(OuterOaDepartmentBindEntity::getOutDepId, outDepId);
        return outerOaDepartmentBindMapper.selectOne(wrapper);
    }

    public List<OuterOaDepartmentBindEntity> queryOuterOaDepartmentBindEntities(ChannelEnum channel, String fsEa, String outEa, String appId, List<String> fsDepIds, List<String> outDepIds) {
        LambdaQueryWrapper<OuterOaDepartmentBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaDepartmentBindEntity::getChannel, channel);
        if (StringUtils.isNotEmpty(fsEa)) {
            wrapper.eq(OuterOaDepartmentBindEntity::getFsEa, fsEa);
        }
        if (StringUtils.isNotEmpty(outEa)) {
            wrapper.eq(OuterOaDepartmentBindEntity::getOutEa, outEa);
        }
        if (StringUtils.isNotEmpty(appId)) {
            wrapper.eq(OuterOaDepartmentBindEntity::getAppId, appId);
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(fsDepIds)) {
            wrapper.in(OuterOaDepartmentBindEntity::getFsDepId, fsDepIds);
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(outDepIds)) {
            wrapper.in(OuterOaDepartmentBindEntity::getOutDepId, outDepIds);
        }
        return outerOaDepartmentBindMapper.selectList(wrapper);
    }

    public Integer updateStatusByFsDeptIds(ChannelEnum channel, String fsEa, String outEa, String appId, List<String> fsDeptIds, BindStatusEnum status) {
        LambdaUpdateWrapper<OuterOaDepartmentBindEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(OuterOaDepartmentBindEntity::getChannel, channel);
        wrapper.eq(OuterOaDepartmentBindEntity::getOutEa, outEa);
        wrapper.eq(OuterOaDepartmentBindEntity::getFsEa, fsEa);
        wrapper.eq(OuterOaDepartmentBindEntity::getAppId, appId);
        wrapper.in(OuterOaDepartmentBindEntity::getFsDepId, fsDeptIds);
        wrapper.set(OuterOaDepartmentBindEntity::getBindStatus, status);
        return outerOaDepartmentBindMapper.update(null, wrapper);
    }

    /**
     * 根据参数查询部门绑定关系
     *
     * @param params 查询参数
     * @return 部门绑定列表
     */
    public List<OuterOaDepartmentBindEntity> getEntities(OuterOaDepartmentBindParams params) {
        LambdaQueryWrapper<OuterOaDepartmentBindEntity> queryWrapper = new LambdaQueryWrapper<>();
        if (params != null) {
            if (StringUtils.isNotBlank(params.getOutDepId())) {
                queryWrapper.eq(OuterOaDepartmentBindEntity::getOutDepId, params.getOutDepId());
            }
            if (StringUtils.isNotBlank(params.getFsDepId())) {
                queryWrapper.eq(OuterOaDepartmentBindEntity::getFsDepId, params.getFsDepId());
            }
            if (params.getChannel() != null) {
                queryWrapper.eq(OuterOaDepartmentBindEntity::getChannel, params.getChannel());
            }
            if (StringUtils.isNotBlank(params.getOutEa())) {
                queryWrapper.eq(OuterOaDepartmentBindEntity::getOutEa, params.getOutEa());
            }
            if (StringUtils.isNotBlank(params.getFsEa())) {
                queryWrapper.eq(OuterOaDepartmentBindEntity::getFsEa, params.getFsEa());
            }
            if (StringUtils.isNotBlank(params.getAppId())) {
                queryWrapper.eq(OuterOaDepartmentBindEntity::getAppId, params.getAppId());
            }
            if (params.getBindStatus() != null) {
                queryWrapper.eq(OuterOaDepartmentBindEntity::getBindStatus, params.getBindStatus());
            }
        }
        return outerOaDepartmentBindMapper.selectList(queryWrapper);
    }

    /**
     * 根据ID查询部门绑定
     *
     * @param id ID
     * @return 部门绑定实体
     */
    public OuterOaDepartmentBindEntity getById(String id) {
        return outerOaDepartmentBindMapper.selectById(id);
    }

    /**
     * 根据外部部门ID查询部门绑定
     *
     * @param channel   渠道
     * @param outEa     外部企业账号
     * @param appId     应用ID
     * @param outDeptId 外部部门ID
     * @return 部门绑定实体
     */
    public OuterOaDepartmentBindEntity getByOutDeptId(ChannelEnum channel, String outEa,String fsEa, String appId,
                                                      String outDeptId) {
        LambdaQueryWrapper<OuterOaDepartmentBindEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OuterOaDepartmentBindEntity::getOutEa, outEa)
                .eq(OuterOaDepartmentBindEntity::getChannel, channel)
                .eq(OuterOaDepartmentBindEntity::getAppId, appId)
                .eq(OuterOaDepartmentBindEntity::getOutDepId, outDeptId)
                .eq(OuterOaDepartmentBindEntity::getFsEa, fsEa)
                .eq(OuterOaDepartmentBindEntity::getBindStatus, BindStatusEnum.normal);
        return outerOaDepartmentBindMapper.selectOne(queryWrapper);
    }

    /**
     * 删除部门绑定关系
     */
    public Integer deleteByOutDepId(ChannelEnum channel, String fsEa, String appId, String outDepId) {
        LambdaQueryWrapper<OuterOaDepartmentBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaDepartmentBindEntity::getChannel, channel)
                .eq(OuterOaDepartmentBindEntity::getFsEa, fsEa)
                .eq(OuterOaDepartmentBindEntity::getOutDepId, outDepId)
                .eq(OuterOaDepartmentBindEntity::getAppId, appId);
        return outerOaDepartmentBindMapper.delete(wrapper);
    }

    /**
     * 解除绑定
     * 根据数据中心ID和CRM部门ID更新对应的绑定状态为停用状态
     *
     * @param dataCenterId 数据中心ID
     * @param crmDeptId CRM部门ID（纷享部门ID）
     * @return 影响的行数
     */
    public Integer unbindDeptStatus(String dataCenterId, String crmDeptId) {
        if (StringUtils.isEmpty(dataCenterId) || StringUtils.isEmpty(crmDeptId)) {
            log.warn("unbindDeptStatus参数不能为空, dataCenterId: {}, crmDeptId: {}", dataCenterId, crmDeptId);
            return 0;
        }

        LambdaUpdateWrapper<OuterOaDepartmentBindEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(OuterOaDepartmentBindEntity::getDcId, dataCenterId)
               .eq(OuterOaDepartmentBindEntity::getFsDepId, crmDeptId);

        // 设置更新字段
        wrapper.set(OuterOaDepartmentBindEntity::getBindStatus, BindStatusEnum.stop)
               .set(OuterOaDepartmentBindEntity::getUpdateTime, System.currentTimeMillis());

        int result = outerOaDepartmentBindMapper.update(null, wrapper);
        log.info("unbind dept, dataCenterId: {}, crmDeptId: {}, affect rows: {}", dataCenterId, crmDeptId, result);
        return result;
    }

    /**
     * 分页查询部门列表
     */
    public List<OuterOaDepartmentBindEntity> findNormalDepartments(ChannelEnum channel, String fsEa, String appId, Integer page, Integer size) {
        LambdaQueryWrapper<OuterOaDepartmentBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaDepartmentBindEntity::getChannel, channel)
                .eq(OuterOaDepartmentBindEntity::getFsEa, fsEa)
                .eq(OuterOaDepartmentBindEntity::getAppId, appId)
                .eq(OuterOaDepartmentBindEntity::getBindStatus, BindStatusEnum.normal);

        Page<OuterOaDepartmentBindEntity> pageParam = new Page<>(page, size);
        Page<OuterOaDepartmentBindEntity> pageResult = outerOaDepartmentBindMapper.selectPage(pageParam, wrapper);
        return pageResult.getRecords();
    }

    /**
     * 批量查询部门绑定关系
     */
    public List<OuterOaDepartmentBindEntity> batchGetByOutDepIds(ChannelEnum channel, String fsEa, String appId, List<String> outDepIds) {
        if (CollectionUtils.isEmpty(outDepIds)) {
            return null;
        }

        LambdaQueryWrapper<OuterOaDepartmentBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaDepartmentBindEntity::getChannel, channel)
                .eq(OuterOaDepartmentBindEntity::getFsEa, fsEa)
                .eq(OuterOaDepartmentBindEntity::getAppId, appId)
                .in(OuterOaDepartmentBindEntity::getOutDepId, outDepIds)
                .eq(OuterOaDepartmentBindEntity::getBindStatus, BindStatusEnum.normal);

        return outerOaDepartmentBindMapper.selectList(wrapper);
    }

    /**
     * 查询状态正常的部门绑定实体列表
     * @param channel 渠道
     * @param fsEa 纷享企业账号
     * @param outEa 外部企业账号
     * @param appId 应用ID
     * @param fsDepIds 纷享部门ID列表
     * @param outDepIds 外部部门ID列表
     * @return 状态正常的部门绑定实体列表
     */
    public List<OuterOaDepartmentBindEntity> queryNormalOuterOaDepartmentBindEntities(ChannelEnum channel, String fsEa, String outEa, String appId, List<String> fsDepIds, List<String> outDepIds) {
        LambdaQueryWrapper<OuterOaDepartmentBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaDepartmentBindEntity::getChannel, channel);
        wrapper.eq(OuterOaDepartmentBindEntity::getBindStatus, BindStatusEnum.normal);
        if (StringUtils.isNotEmpty(fsEa)) {
            wrapper.eq(OuterOaDepartmentBindEntity::getFsEa, fsEa);
        }
        if (StringUtils.isNotEmpty(outEa)) {
            wrapper.eq(OuterOaDepartmentBindEntity::getOutEa, outEa);
        }
        if (StringUtils.isNotEmpty(appId)) {
            wrapper.eq(OuterOaDepartmentBindEntity::getAppId, appId);
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(fsDepIds)) {
            wrapper.in(OuterOaDepartmentBindEntity::getFsDepId, fsDepIds);
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(outDepIds)) {
            wrapper.in(OuterOaDepartmentBindEntity::getOutDepId, outDepIds);
        }
        return outerOaDepartmentBindMapper.selectList(wrapper);
    }
}
