package com.facishare.open.ding.provider.manager;

import cn.hutool.core.lang.Pair;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.ding.api.result.DingEnterpriseResult;
import com.facishare.open.ding.api.service.EnterpriseService;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.result.ResultCode;
import com.facishare.open.ding.provider.dingding.DingRequestUtil;
import com.github.benmanes.caffeine.cache.CacheLoader;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.checkerframework.checker.nullness.qual.NonNull;
import org.checkerframework.checker.nullness.qual.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/3/13 14:48:43
 */
@Slf4j
@Component
public class TokenManager {

    @Autowired
    private DingEnterpriseManager dingEnterpriseManager;

    @Autowired
    private EnterpriseService enterpriseService;

    @Autowired
    private EIEAConverter eieaConverter;

    /**
     * key: ei + appId
     * value: token
     */
    private LoadingCache<Pair<Integer, String>, String> tokenCache = Caffeine.newBuilder().maximumSize(10000).expireAfterWrite(100, TimeUnit.MINUTES).refreshAfterWrite(90, TimeUnit.MINUTES).build(new CacheLoader<Pair<Integer, String>, String>() {
        @Nullable
        @Override
        public String load(@NonNull Pair<Integer, String> key) throws Exception {
            // 由于需要获取所有企业的信息,这里传入null作为appId
            Result<DingEnterpriseResult> dingEnterpriseResultResult = dingEnterpriseManager.queryEnterpriseByEi(key.getKey(), key.getValue());
            DingEnterpriseResult result = dingEnterpriseResultResult.getData();
            return DingRequestUtil.getToken(result.getEi(), result.getClientIp(), result.getAppKey(), result.getAppSecret(), result.getNginxIp());
        }
    });

    /**
     * key: ea + appId
     * value: ticket
     */
    private LoadingCache<Pair<String, String>, String> ticketCache = Caffeine.newBuilder().maximumSize(10000).expireAfterWrite(100, TimeUnit.MINUTES).refreshAfterWrite(90, TimeUnit.MINUTES).build(new CacheLoader<Pair<String, String>, String>() {
        @Nullable
        @Override
        public String load(@NonNull Pair<String, String> key) throws Exception {
            final String ea = key.getKey();
            final String appId = key.getValue();
            final int ei = eieaConverter.enterpriseAccountToId(ea);
            String token= getToken(ei, appId);

            Result<DingEnterpriseResult> dingEnterpriseResultResult = enterpriseService.queryEnterpriseByEa(ea, appId);
            if (!dingEnterpriseResultResult.isSuccess() || dingEnterpriseResultResult.getData() == null) {
                return null;
            }
            Result<String> jsApiSecret = DingRequestUtil.getJsApiSecret(ei, dingEnterpriseResultResult.getData().getClientIp(), token, dingEnterpriseResultResult.getData().getNginxIp());
            if (ObjectUtils.isEmpty(jsApiSecret)) {
                return null;
            }
            if(jsApiSecret.getErrorCode()== ResultCode.ACCESS_TOKEN_INVALID.getErrorCode()){
                String cacheToken = DingRequestUtil.getToken(dingEnterpriseResultResult.getData().getEi(), dingEnterpriseResultResult.getData().getClientIp(), dingEnterpriseResultResult.getData().getAppKey(), dingEnterpriseResultResult.getData().getAppSecret(), dingEnterpriseResultResult.getData().getNginxIp());
                putToken(ei, appId, cacheToken);
                Result<String> jsApiSecret1 = DingRequestUtil.getJsApiSecret(ei, dingEnterpriseResultResult.getData().getClientIp(), token, dingEnterpriseResultResult.getData().getNginxIp());
                if (ObjectUtils.isEmpty(jsApiSecret1)) {
                    return null;
                }
                return jsApiSecret1.getData();

            }
            return jsApiSecret.getData();
        }
    });

    public String getToken(Integer ei, String appId) {
        return tokenCache.get(Pair.of(ei, appId));
    }

    public void invalidate(Integer ei, String appId) {
        tokenCache.invalidate(Pair.of(ei, appId));
    }

    public void putToken(Integer ei, String appId, String refreshToken) {
        tokenCache.put(Pair.of(ei, appId), refreshToken);
    }

    public String getTicket(String enterPriseAccount, String appKey) {
        return ticketCache.get(Pair.of(enterPriseAccount, appKey));
    }
}
