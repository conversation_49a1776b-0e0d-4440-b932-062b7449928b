package com.facishare.open.feishu.syncapi.service;

import com.facishare.open.feishu.syncapi.model.login.FsUserModel;
import com.facishare.open.feishu.syncapi.model.login.LoginAuthModel;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.data.Code2UserInfoData;
import com.facishare.open.outer.oa.connector.common.api.login.UserTicketModel;

public interface LoginService {
    /**
     * 飞书免登录code换飞书用户身份
     * @param loginAuthModel
     * @return
     */
    Result<Code2UserInfoData> code2UserInfo(LoginAuthModel loginAuthModel);

    /**
     * 生成纷享ticket
     * @param userTicketModel
     * @return
     */
    Result<String> genFsTicket(UserTicketModel userTicketModel);

    /**
     * 根据ticket获取纷享用户信息
     * @param ticket
     * @return
     */
    Result<FsUserModel> getFsUser(String ticket);
}