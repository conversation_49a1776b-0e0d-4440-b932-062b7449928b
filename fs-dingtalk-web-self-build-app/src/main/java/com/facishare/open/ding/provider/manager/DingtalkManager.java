package com.facishare.open.ding.provider.manager;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.open.ding.api.result.DingEnterpriseResult;
import com.facishare.open.ding.api.result.DingMappingEmployeeResult;
import com.facishare.open.ding.api.vo.DingTaskVo;
import com.facishare.open.ding.common.model.bizlog.DingAPICallNumer;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.result.ResultCode;
import com.facishare.open.ding.common.utils.ConvertUrlUtils;
import com.facishare.open.ding.provider.config.ConfigCenter;
import com.facishare.open.ding.provider.dingding.DingRequestUtil;
import com.facishare.open.ding.provider.dingding.DingUrl;
import com.facishare.open.ding.provider.enums.DingTodoTypeEnum;
import com.facishare.open.ding.provider.handler.DingRetryHandlerFromProvider;
import com.facishare.open.ding.provider.handler.QPSLimitHandlerFromProvider;
import com.facishare.open.ding.provider.model.HttpResponseMessage;
import com.facishare.open.ding.provider.utils.XorUtils;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.message.extrnal.platform.model.arg.CreateTodoArg;
import com.fxiaoke.message.extrnal.platform.model.arg.DealTodoArg;
import com.fxiaoke.message.extrnal.platform.model.arg.DeleteTodoArg;
import com.fxiaoke.message.extrnal.platform.model.result.CreateTodoResult;
import com.fxiaoke.message.extrnal.platform.model.result.DealTodoResult;
import com.fxiaoke.message.extrnal.platform.model.result.DeleteTodoResult;
import com.fxiaoke.otherrestapi.function.arg.FunctionServiceExecuteArg;
import com.fxiaoke.otherrestapi.function.data.FunctionServiceParameterData;
import com.fxiaoke.otherrestapi.function.data.HeaderObj;
import com.fxiaoke.ps.ProtostuffUtil;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.URLEncoder;
import java.rmi.RemoteException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class DingtalkManager {

    @Autowired
    private TokenManager tokenManager;

    @Autowired
    private DingTodoManager dingTodoManager;

    @Autowired
    private DingMappingEmployeeManager dingMappingEmployeeManager;

    @Autowired
    private SfaApiManager sfaApiManager;

    @ReloadableProperty("mid.authorize.url")
    private String MID_URL;

    //客户端
    private static final String DING_SINGLE_URL = "https://oapi.dingtalk.com/connect/oauth2/sns_authorize?response_type=code&scope=snsapi_auth&state=STATE";

    private static final String DING_PC_SLIDE_URL = "dingtalk://dingtalkclient/page/link?url=%s&pc_slide=true";
    private static final String DING_WORK_OPEN_URL="dingtalk://dingtalkclient/action/openapp?corpid=%s&container_type=work_platform&app_id=0_%s&redirect_type=jump&redirect_url=%s";
    @Autowired
    private QPSLimitHandlerFromProvider qpsLimitHandlerFromProvider;
    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;


    public Result<List<DingTodoTypeEnum>> filterMsg(String functionName, Map<String, Object> sourceMessage, Integer ei) {
        FunctionServiceExecuteArg arg = new FunctionServiceExecuteArg();
        HeaderObj headerObj = new HeaderObj(ei, -10000);
        arg.setApiName(functionName);
        arg.setBindingObjectAPIName("NONE");
        arg.setNameSpace("controller");

        List<FunctionServiceParameterData> parameters = new LinkedList<>();
        FunctionServiceParameterData<Map> data = new FunctionServiceParameterData();
        data.setName("sourceMsg");
        data.setType("Map");
        data.setValue(sourceMessage);
        parameters.add(data);
        arg.setParameters(parameters);
         com.facishare.open.outer.oa.connector.common.api.result.Result<Object> result=null;
        boolean isContinue = true;
        List<DingTodoTypeEnum> typeEnums = null;
        try {
            result = sfaApiManager.executeCustomFunction(headerObj, arg);
            if (result == null || !result.isSuccess() || ObjectUtils.isEmpty(result.getData())) {
                log.warn("dingtalk.sendQyWeixinMsg,objectResult={}", result);
                return Result.newError(ResultCode.FUNCTION_FILTER_MESSAGE_ERROR);
            }
            //判断是否可以发送
            Map<String, Object> objectMap = new Gson().fromJson(result.getData().toString(), new TypeToken<Map<String, Object>>() {
            });
            isContinue = Boolean.parseBoolean(objectMap.get("continue").toString());
            final Object types = objectMap.get("types");
            if (Objects.nonNull(types) && types instanceof List) {
                typeEnums = ((List<?>) types).stream()
                        .map(type -> {
                            if (StringUtils.equalsIgnoreCase(type.toString(), "todo")) {
                                return DingTodoTypeEnum.DING_TODO;
                            } else if (StringUtils.equalsIgnoreCase(type.toString(), "work")) {
                                return DingTodoTypeEnum.DING_WORK;
                            } else {
                                return null;
                            }
                        }).filter(Objects::nonNull)
                        .collect(Collectors.toList());
            }
            log.info("QYWeixinMessageSendServiceImpl.sendQyWeixinMsg,isContinue={}", isContinue);
            if (!isContinue) {
                return Result.newError(ResultCode.TODO_MESSAGE_FILTER_NOT_SEND);
            }
        } catch (RemoteException e) {
            log.warn("executeCustomFunction error,arg={}", arg, e);
            return Result.newError(ResultCode.FUNCTION_FILTER_MESSAGE_ERROR);
        }
        return Result.newSuccess(typeEnums);
    }

    public CreateTodoResult createTodo(CreateTodoArg arg, DingEnterpriseResult mappingEnterprise) {


        Gson gson = new Gson();
        CreateTodoResult result = new CreateTodoResult();
        log.info("Enter the gray-scale enterprise agency process");


        //去掉crm与钉钉无绑定的人员的账号
        List<Integer> receiverIds = new LinkedList<>();
        final String appId = mappingEnterprise.getAppKey();
        for (int i = 0; i < arg.getReceiverIds().size(); i++) {
            if (dingMappingEmployeeManager.checkEmpBindByEmpId(arg.getEi(), arg.getReceiverIds().get(i), appId)) {
                receiverIds.add(arg.getReceiverIds().get(i));
            }
        }
        if (CollectionUtils.isEmpty(receiverIds)) {
            log.info("the receivers is not binded.");
            return result;
        }
        log.info("externalOaTodoServiceImpl.createTodo.receiverIds = {}", receiverIds);

        String accessToken = tokenManager.getToken(arg.getEi(), appId);
        Map<String, String> dingTalkToken = Maps.newHashMap();
        dingTalkToken.put("x-acs-dingtalk-access-token", accessToken);
        log.info("externalOaTodoServiceImpl.createTodo.accessToken = {}", XorUtils.EncodeByXor(accessToken, ConfigCenter.XOR_SECRET_KEY));

        Map<String, Object> dingTalk = Maps.newHashMap();
        dingTalk.put("sourceId", arg.getSourceId());
        //标志
        Boolean flag = false;

        List<DingMappingEmployeeResult> employeeLists = ListUtils.emptyIfNull(dingMappingEmployeeManager.batchGetDingEmployeesByFsIds(arg.getEi(), receiverIds, appId).getData()).stream().filter(employee -> {
            final boolean empty = StringUtils.isEmpty(employee.getDingUnionId());
            if (empty) {
                log.info("userUnionId is null employee:{}", employee);
            }
            return !empty;
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(employeeLists)) {
            log.warn("userUnionIds is empty, arg={}.", arg);
            result.setMessage("userUnionIds is empty.");
            return result;
        }
        List<String> userUnionIds = employeeLists.stream().map(DingMappingEmployeeResult::getDingUnionId).collect(Collectors.toList());
        final Map<Integer, String> map = employeeLists.stream().collect(Collectors.toMap(DingMappingEmployeeResult::getEmployeeId, DingMappingEmployeeResult::getDingEmployeeId, (o1, o2) -> o1));
        int operatorId = employeeLists.get(0).getEmployeeId();
        String operatorUnionId = employeeLists.get(0).getDingUnionId();
        log.info("externalOaTodoServiceImpl.createTodo.ea={},userUnionIds={}", arg.getEa(), userUnionIds);
        //临时解决
        Map<String, String> sysUserIdMap = new Gson().fromJson(ConfigCenter.SYS_TODO_USER_IDS, new TypeToken<Map<String, String>>() {
        }.getType());
        if (sysUserIdMap.containsKey(arg.getEa())) {
            operatorId = -10000;
            operatorUnionId = sysUserIdMap.get(arg.getEa());
            log.info("externalOaTodoServiceImpl.createTodo.ea={},operatorUnionId={}.", arg.getEa(), operatorUnionId);
        }
        //查看代办详情
        String dealTalkUrl = ConvertUrlUtils.convertDingUrl(DingUrl.DINGTALK_TODO, mappingEnterprise.getNginxIp()) + operatorUnionId + "/tasks/sources/" + arg.getSourceId();
        Boolean isQPSLimit3 = qpsLimitHandlerFromProvider.isQPSLimitByEa(arg.getEa());
        if (isQPSLimit3) {
            //限流且重试多次失败
            log.info("externalOaTodoServiceImpl.createTodo.qps limit.arg={}", arg);
            return result;
        }
        HttpResponseMessage messageResult = null;
        JSONObject jsonObject = null;
        if (mappingEnterprise.getClientIp() != null) {
            Map<String, Object> messageArg = new HashMap<>();
            messageArg.put("url", dealTalkUrl);//钉钉发送消息的url
            messageArg.put("type", "GET");
            messageArg.put("header", gson.toJson(dingTalkToken));
            messageArg.put("token", mappingEnterprise.getToken());
            String clientUrl = DingRequestUtil.appendProxyHttpUrl(mappingEnterprise.getClientIp());
            Map<String, String> headers = new HashMap<>();
            headers.put("tenantId", String.valueOf(arg.getEi()));
            messageResult = DingRetryHandlerFromProvider.sendOkHttp3Post(clientUrl, headers, gson.toJson(messageArg));
            if (ObjectUtils.isEmpty(messageResult)) {
                log.warn("messageHttpResult is null, arg={}.", arg);
                result.setMessage("messageResult is null.");
                return result;
            }
            JSONObject messageJsonObject = JSONObject.parseObject(messageResult.getContent());
            if ((Integer) messageJsonObject.get("errorCode") != 200) {
                log.warn("messageHttpResult is error, arg={}.", arg);
                result.setMessage("messageResult is error.");
                return result;
            }
            jsonObject = JSONObject.parseObject(messageJsonObject.get("data").toString());
        } else {
            dingTalkToken.put("tenantId", String.valueOf(arg.getEi()));
            messageResult = DingRetryHandlerFromProvider.sendOkHttp3Get(dealTalkUrl, dingTalkToken, new HashMap<>());
            log.info("externalOaTodoServiceImpl.createTodo.createTalkUrl={},messageResult={}.", dealTalkUrl, messageResult);
            if (ObjectUtils.isEmpty(messageResult)) {
                log.warn("messageHttpResult is null, arg={}.", arg);
                result.setMessage("messageResult is null.");
                return result;
            }
            if (messageResult.getStatusCode() != 200) {
                log.warn("messageResult is error, arg={}.", arg);
                result.setMessage("messageResult is error.");
                return result;
            }
            jsonObject = JSONObject.parseObject(messageResult.getContent());
        }
        String talkId = String.valueOf(jsonObject.get("id"));
        if (StringUtils.isEmpty(talkId) || "null".equals(talkId)) {
            //找不到代办，新建代办
            //优先级
            int priority = 20;
            //优化卡片消息
            StringBuilder markdown = new StringBuilder();
            StringBuilder subject = new StringBuilder();
            StringBuilder description = new StringBuilder();
            if (StringUtils.isNotEmpty(arg.getTitle())) {
                subject.append(arg.getTitle()).append("-");
            }
            description.append(arg.getContent()).append("-");
            if (CollectionUtils.isNotEmpty(arg.getForm())) {
                for (int i = 0; i < arg.getForm().size(); i++) {
                    markdown.append("【").append(arg.getForm().get(i).getKey()).append(": ").append(arg.getForm().get(i).getValue()).append("】");
                    if (i == 1) {
                        subject.append(markdown);
                    }
                }
            }
            description.append(markdown);
            dingTalk.put("subject", subject.toString());
            dingTalk.put("description", description.toString());
            //跳转
            String objectApiName = arg.getExtraDataMap().get("objectApiName");
            String objectId = arg.getExtraDataMap().get("objectId");
            String instanceId = arg.getExtraDataMap().get("workflowInstanceId");
            String directUri = MID_URL + "?ei=" + arg.getEi() + "&apiname=" + objectApiName
                    + "&id=" + objectId + "&instanceId=" + instanceId + "&taskId=" + arg.getGenerateUrlType() + "&bizType=" + arg.getBizType();
            StringBuilder stringBuilder = new StringBuilder();
            String directAppId = mappingEnterprise.getRedirectAppId();
            stringBuilder.append(ConvertUrlUtils.convertDingUrl(DING_SINGLE_URL, mappingEnterprise.getNginxIp())).append("&appid=").append(directAppId).append("&redirect_uri=").append(URLEncoder.encode(directUri));
            log.info("finalUrl = {}", stringBuilder.toString());
            Map<String, String> detailUrl = Maps.newHashMap();
            detailUrl.put("appUrl", stringBuilder.toString());
            String pcUrl = String.format(DING_PC_SLIDE_URL, URLEncoder.encode(stringBuilder.toString()));
            if(ConfigCenter.TODO_SLIDER_PC_OPEN_TENANTS_BROWSER.contains(arg.getEa())){
                pcUrl=pcUrl.replace("&pc_slide=true","&pc_slide=false");
            }
            if(ConfigCenter.TODO_SLIDER_PC_OPEN_TENANTS_WORK_BENCH.contains(arg.getEa())){
                pcUrl = String.format(DING_WORK_OPEN_URL,mappingEnterprise.getDingCorpId(),mappingEnterprise.getAgentId(), URLEncoder.encode(stringBuilder.toString()));
            }
            log.info("todo slider open url:{}",pcUrl);
            detailUrl.put("pcUrl", pcUrl);
            dingTalk.put("creatorId", operatorUnionId);
            dingTalk.put("detailUrl", detailUrl);
            //设置代办任务仅出现在处理人当中，创建者的钉钉就不会出现不需要处理的代办
            boolean isOnlyShowExecutor = true;
            dingTalk.put("isOnlyShowExecutor", isOnlyShowExecutor);
            dingTalk.put("priority", priority);
            dingTalk.put("executorIds", userUnionIds);
            dingTalk.put("participantIds", userUnionIds);
            //设置截止时间，钉钉代办可以显示飘数，约定截止时间为当天的23:59:59，过期也可以显示飘数，不影响页面的跳转
            try {
                String endTime = DateFormatUtils.format(new Date(), "yyyy-MM-dd 23:59:59");
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                Date datetime = sdf.parse(endTime);//将你的日期转换为时间戳
                long time = datetime.getTime();
                dingTalk.put("dueTime", time);
            } catch (ParseException e) {
                e.printStackTrace();
            }
            //调用钉钉代办第三方接口
            String dingTalkUrl = ConvertUrlUtils.convertDingUrl(DingUrl.DINGTALK_TODO, mappingEnterprise.getNginxIp()) + operatorUnionId + "/tasks?operatorId=" + operatorUnionId;
            Boolean isQPSLimit1 = qpsLimitHandlerFromProvider.isQPSLimitByEa(arg.getEa());
            if (isQPSLimit1) {
                //限流且重试多次失败
                log.info("externalOaTodoServiceImpl.createTodo.qps limit.arg1={}", arg);
                return result;
            }

            try {
                for (int crmUserId : receiverIds) {//按照Userid分开上报是为了能快速识别哪些userid用了最多的API
                    DingAPICallNumer dumpLog = DingAPICallNumer.builder().tenantId(String.valueOf(arg.getEi()))
                            .bizName(arg.getBizType()).title(arg.getTitle()).senderId(arg.getSenderId()).appid(appId).crmUserId(crmUserId)
                            .objectApiName(arg.getExtraDataMap().get("objectApiName")).objectId(arg.getExtraDataMap().get("objectId"))
                            .functionName("createTodo")
                            .build();
                    BizLogClient.send("generic-biz-log", ProtostuffUtil.serializer(dumpLog));
                }
            } catch (Exception ex) {
            }

            HttpResponseMessage CreateMessageResult = null;
            JSONObject createJsonObject = null;
            Integer createCode = null;
            String createContent = null;
            if (mappingEnterprise.getClientIp() != null) {
                Map<String, Object> messageArg = new HashMap<>();
                messageArg.put("url", dingTalkUrl);//钉钉发送消息的url
                messageArg.put("type", "POST");
                messageArg.put("header", gson.toJson(dingTalkToken));
                messageArg.put("token", mappingEnterprise.getToken());
                messageArg.put("data", dingTalk);
                String clientUrl = DingRequestUtil.appendProxyHttpUrl(mappingEnterprise.getClientIp());
                Map<String, String> headers = new HashMap<>();
                headers.put("tenantId", String.valueOf(arg.getEi()));
                CreateMessageResult = DingRetryHandlerFromProvider.sendOkHttp3Post(clientUrl, headers, gson.toJson(messageArg));
                if (CreateMessageResult == null) {
                    log.info("externalOaTodoServiceImpl.createTodo.create.ea={},dingTalk={},updateToDo={},CreateMessageResult={}.", arg.getEa(), dingTalk, dingTalkUrl, CreateMessageResult);
                    return result;
                }
                JSONObject createMsgJsonObject = JSONObject.parseObject(CreateMessageResult.getContent());
                createCode = (Integer) createMsgJsonObject.get("errorCode");
                createContent = createMsgJsonObject.get("data").toString();
            } else {
                dingTalkToken.put("tenantId", String.valueOf(arg.getEi()));
                CreateMessageResult = DingRetryHandlerFromProvider.sendOkHttp3Post(dingTalkUrl, dingTalkToken, gson.toJson(dingTalk));
                if (CreateMessageResult == null) {
                    log.info("externalOaTodoServiceImpl.createTodo.create.ea={},dingTalk={},updateToDo={},CreateMessageResult={}.", arg.getEa(), dingTalk, dingTalkUrl, CreateMessageResult);
                    return result;
                }
                createCode = CreateMessageResult.getStatusCode();
                createContent = CreateMessageResult.getContent();
            }
            log.info("externalOaTodoServiceImpl.createTodo.create.ea={},dingTalk={},updateToDo={},CreateMessageResult={}.", arg.getEa(), dingTalk, dingTalkUrl, CreateMessageResult);
            if (createCode == 200) {
                createJsonObject = JSONObject.parseObject(createContent);
                log.warn("ExternalOaTodoServiceImpl.createTodo: 新增代办成功，ea={}，messageArg={}，userId={}.", arg.getEa(), dingTalk, receiverIds);
                final String dcId = outerOaEnterpriseBindManager.getDcIdByEaAndAppId(ChannelEnum.dingding, arg.getEa(), appId);
                for (int i = 0; i < receiverIds.size(); i++) {
                    final Integer employeeId = receiverIds.get(i);
                    DingTaskVo dingTaskVo = new DingTaskVo();
//                    dingTaskVo.setEmployeeSourceId();
                    dingTaskVo.setDataCenterId(dcId);
                    dingTaskVo.setMessageType(DingTodoTypeEnum.DING_TODO.name());
                    dingTaskVo.setDingEmployeeId(map.get(employeeId));
                    dingTaskVo.setStatus(1);
                    dingTaskVo.setEi(arg.getEi());
                    dingTaskVo.setEmployeeId(employeeId);
                    dingTaskVo.setSourceId(arg.getSourceId());
                    dingTaskVo.setCreatorEmployeeId(operatorId);
                    dingTaskVo.setTaskId(String.valueOf(createJsonObject.get("id")));
                    int update = insertSource(dingTaskVo, appId);
                    if (update >= 1) {
                        log.info("ExternalOaTodoServiceImpl.createTodo: 保存关系映射成功， dingTaskVo={}.", dingTaskVo);
                    }
                }
            } else {
                log.warn("ExternalOaTodoServiceImpl.createTodo: 新增代办失败，ea={}，messageArg={}，userId={}.", arg.getEa(), dingTalk, receiverIds);
            }
        } else {
            //代办已经存在，更新代办

            String executorIdJson = JSONArray.toJSONString(jsonObject.get("executorIds"));
            // 将json字符串转换为list集合
            List<String> executorIds = CollectionUtils.isNotEmpty(JSONArray.parseArray(executorIdJson, String.class)) ? JSONArray.parseArray(executorIdJson, String.class) : new LinkedList<>();
            executorIds.addAll(userUnionIds);
            String participantIdJson = JSONArray.toJSONString(jsonObject.get("participantIds"));
            // 将json字符串转换为list集合
            List<String> participantIds = CollectionUtils.isNotEmpty(JSONArray.parseArray(participantIdJson, String.class)) ? JSONArray.parseArray(participantIdJson, String.class) : new LinkedList<>();
            participantIds.addAll(userUnionIds);
            dingTalk.put("done", flag);
            dingTalk.put("executorIds", executorIds);
            dingTalk.put("participantIds", participantIds);
            String updateToDo = ConvertUrlUtils.convertDingUrl(DingUrl.DINGTALK_TODO, mappingEnterprise.getNginxIp()) + operatorUnionId + "/tasks/" + talkId + "?operatorId=" + operatorUnionId;
            Boolean isQPSLimit2 = qpsLimitHandlerFromProvider.isQPSLimitByEa(arg.getEa());
            if (isQPSLimit2) {
                //限流且重试多次失败
                log.info("externalOaTodoServiceImpl.createTodo.qps limit.arg2={}", arg);
                return result;
            }

            try {
                for (int crmUserId : receiverIds) {//按照Userid分开上报是为了能快速识别哪些userid用了最多的API
                    DingAPICallNumer dumpLog = DingAPICallNumer.builder().tenantId(String.valueOf(arg.getEi()))
                            .bizName(arg.getBizType()).title(arg.getTitle()).senderId(arg.getSenderId()).appid(appId).crmUserId(crmUserId)
                            .objectApiName(arg.getExtraDataMap().get("objectApiName")).objectId(arg.getExtraDataMap().get("objectId"))
                            .functionName("updateTodo")
                            .build();
                    BizLogClient.send("generic-biz-log", ProtostuffUtil.serializer(dumpLog));
                }
            } catch (Exception ex) {
            }

            HttpResponseMessage updateToDoMessageResult = null;
            Integer updateCode = null;
            if (mappingEnterprise.getClientIp() != null) {
                Map<String, Object> messageArg = new HashMap<>();
                messageArg.put("url", updateToDo);//钉钉发送消息的url
                messageArg.put("type", "PUT");
                messageArg.put("header", gson.toJson(dingTalkToken));
                messageArg.put("token", mappingEnterprise.getToken());
                messageArg.put("data", dingTalk);
                String clientUrl = DingRequestUtil.appendProxyHttpUrl(mappingEnterprise.getClientIp());
                Map<String, String> headers = new HashMap<>();
                headers.put("tenantId", String.valueOf(arg.getEi()));
                updateToDoMessageResult = DingRetryHandlerFromProvider.sendOkHttp3Post(clientUrl, headers, gson.toJson(messageArg));
                if (updateToDoMessageResult == null) {
                    log.warn("externalOaTodoServiceImpl.createTodo: 更新代办失败，ea={}，messageArg={}，userId={}.", arg.getEa(), dingTalk, receiverIds);
                    return result;
                }
                JSONObject updateMsgJsonObject = JSONObject.parseObject(updateToDoMessageResult.getContent());
                updateCode = (Integer) updateMsgJsonObject.get("errorCode");
            } else {
                dingTalkToken.put("tenantId", String.valueOf(arg.getEi()));
                updateToDoMessageResult = DingRetryHandlerFromProvider.sendOkHttp3Put(updateToDo, dingTalkToken, gson.toJson(dingTalk));
                if (updateToDoMessageResult == null) {
                    log.warn("externalOaTodoServiceImpl.createTodo: 更新代办失败，ea={}，messageArg={}，userId={}.", arg.getEa(), dingTalk, receiverIds);
                    return result;
                }
                updateCode = updateToDoMessageResult.getStatusCode();
            }
            log.info("externalOaTodoServiceImpl.createTodo.update.ea={},dingTalk={},updateToDo={},updateToDoMessageResult={}.", arg.getEa(), dingTalk, updateToDo, updateToDoMessageResult);
            if (updateCode == 200) {
                log.warn("externalOaTodoServiceImpl.createTodo: 更新代办成功，creatorEmployeeId={}更新代办成功.", operatorId);
                for (int i = 0; i < receiverIds.size(); i++) {
                    final Integer employeeId = receiverIds.get(i);
                    DingTaskVo dingTaskVo = new DingTaskVo();
//                    dingTaskVo.setEmployeeSourceId();
//                    dingTaskVo.setDataCenterId(dcId);
                    dingTaskVo.setMessageType(DingTodoTypeEnum.DING_TODO.name());
                    dingTaskVo.setDingEmployeeId(map.get(employeeId));
                    dingTaskVo.setStatus(1);
                    dingTaskVo.setEi(arg.getEi());
                    dingTaskVo.setEmployeeId(employeeId);
                    dingTaskVo.setSourceId(arg.getSourceId());
                    dingTaskVo.setCreatorEmployeeId(operatorId);
                    dingTaskVo.setTaskId(talkId);
                    int update = insertSource(dingTaskVo, appId);
                    if (update >= 1) {
                        log.info("ExternalOaTodoServiceImpl.createTodo: 保存关系映射成功， dingTaskVo={}.", dingTaskVo);
                    }
                }
            } else {
                log.warn("ExternalOaTodoServiceImpl.createTodo: 新增代办失败，messageArg={}，userId={}.", dingTalk, receiverIds);
            }
        }
        return result;
    }

    public DealTodoResult dealTodo(DealTodoArg var1, DingEnterpriseResult mappingEnterprise) {
        //处理代办
        log.info("Enter the dealTodo resource");
        DealTodoResult result = new DealTodoResult();
        result.setCode(200);
//        Result<DingEnterpriseResult> mappingEnterprise = dingEnterpriseManager.queryEnterpriseInfoByEi(var1.getEi());
//        if (Objects.isNull(mappingEnterprise) || Objects.isNull(mappingEnterprise.getData())) {
//            log.warn("ei={}.", var1.getEi());
//            return result;
//        }
//        log.info("ExternalOaTodoServiceImpl.dealTodo: dealToDO messageArg:{}", var1);
//        if(!(mappingEnterprise.getData().getAlertStatus().equals(AlertStatusEnum.TODO_STATUS.getStatus()) || mappingEnterprise.getData().getAlertStatus().equals(AlertStatusEnum.TODO_AND_REMIND_STATUS.getStatus()))){
//            //根据source判断这个待办是否还没有处理
//            List<DingTaskVo> dingTaskVo = dingTodoManager.getDingTaskVo(var1.getEi(), var1.getSourceId());
//            if(CollectionUtils.isEmpty(dingTaskVo)) {
//                //已经关闭就不再发送crm待办消息
//                log.info("externalOaTodoServiceImpl.createTodo,crm alert status was closed arg:{}",var1);
//                result.setMessage("crm alert was closed");
//                return result;
//            }
//        }

        //去掉crm与钉钉无绑定的人员的账号
        List<Integer> operators = new LinkedList<>();
        final String appId = mappingEnterprise.getAppKey();
        for (int i = 0; i < var1.getOperators().size(); i++) {
            Boolean isBind = dingMappingEmployeeManager.checkEmpBindByEmpId(var1.getEi(), var1.getOperators().get(i), appId);
            if (isBind) {
                operators.add(var1.getOperators().get(i));
            }
        }
        if (CollectionUtils.isEmpty(operators)) {
            log.info("the receivers is not binded.");
            return result;
        }
        log.info("externalOaTodoServiceImpl.dealTodo.ea={},receiverIds={}", var1.getEa(), operators);
//        Boolean isQPSLimit2 = qpsLimitHandler.isQPSLimitByEa(var1.getEa());
//        if(isQPSLimit2) {
//            //限流且重试多次失败
//            log.info("externalOaTodoServiceImpl.dealTodo.qps limit.var1={}", var1);
//            return result;
//        }
        String accessToken = tokenManager.getToken(var1.getEi(), appId);
        Map<String, String> dingTalkToken = new HashMap<>();
        dingTalkToken.put("x-acs-dingtalk-access-token", accessToken);
        List<DingTaskVo> dingTaskVos = dingTodoManager.getDingTaskVo(var1.getEi(), var1.getSourceId(), appId);
        log.info("externalOaTodoServiceImpl.dealTodo dingTaskVos:{}", dingTaskVos);
        if (CollectionUtils.isEmpty(dingTaskVos)) {
//            log.info("externalOaTodoServiceImpl.dealTodo,ea={},thread sleep.", var1.getEa());
//            //为避免先消费了处理接口，再消费新增接口，需要让线程睡1秒再执行一次查询动作
//            try {
//                Thread.sleep(1000L);
//            } catch (InterruptedException e) {
//                e.printStackTrace();
//            }
//            dingTaskVos = dingTodoManager.getDingTaskVo(var1.getEi(), var1.getSourceId());
//            if(CollectionUtils.isEmpty(dingTaskVos)) {
//                log.info("the dingTaskVos1 is null.");
//                return result;
//            }
            //顺序消费
            log.info("the dingTaskVos1 is null.");
            return result;
        }
        Map<String, Object> dingTalk = Maps.newHashMap();
        List<Map<String, Object>> executorStatusList = new LinkedList<>();
        Boolean flag = Boolean.TRUE;
        List<DingMappingEmployeeResult> employeeLists = ListUtils.emptyIfNull(dingMappingEmployeeManager.batchGetDingEmployeesByFsIds(var1.getEi(), operators, appId).getData()).stream().filter(employee -> {
            final boolean empty = StringUtils.isEmpty(employee.getDingUnionId());
            if (empty) {
                log.info("userUnionId is null employee:{}", employee);
            }
            return !empty;
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(employeeLists)) {
            log.info("the employeeLists is null.");
            return result;
        }
        List<String> userUnionIds = employeeLists.stream().map(DingMappingEmployeeResult::getDingUnionId).collect(Collectors.toList());
        String operatorUnionId = employeeLists.get(0).getDingUnionId();
        for (String userUnionId : userUnionIds) {
            Map<String, Object> executorStatus = Maps.newHashMap();
            executorStatus.put("id", userUnionId);
            executorStatus.put("isDone", flag);
            executorStatusList.add(executorStatus);
        }
        dingTalk.put("executorStatusList", executorStatusList);
        //更换处理接口
        Gson gson = new Gson();
        String dealTalkUrl = ConvertUrlUtils.convertDingUrl(DingUrl.DINGTALK_TODO, mappingEnterprise.getNginxIp()) + operatorUnionId + "/tasks/" + dingTaskVos.get(0).getTaskId() + "/executorStatus?operatorId=" + operatorUnionId;
        Boolean isQPSLimit = qpsLimitHandlerFromProvider.isQPSLimitByEa(var1.getEa());
        if (isQPSLimit) {
            //限流且重试多次失败
            log.info("externalOaTodoServiceImpl.dealTodo.qps limit.var1={}", var1);
            return result;
        }

        try {
            for (int crmUserId : operators) {//按照Userid分开上报是为了能快速识别哪些userid用了最多的API
                DingAPICallNumer dumpLog = DingAPICallNumer.builder().tenantId(String.valueOf(var1.getEi()))
                        .bizName(var1.getBizType()).title("").senderId(0).appid(appId).crmUserId(crmUserId)
                        .objectApiName(var1.getExtraDataMap().get("objectApiName")).objectId(var1.getExtraDataMap().get("objectId"))
                        .functionName("dealTodo")
                        .build();
                BizLogClient.send("generic-biz-log", ProtostuffUtil.serializer(dumpLog));
            }
        } catch (Exception ex) {
        }

        HttpResponseMessage messageResult = null;
        JSONObject jsonObject = null;
        Integer code = null;
        String content = null;
        if (mappingEnterprise.getClientIp() != null) {
            Map<String, Object> messageArg = new HashMap<>();
            messageArg.put("url", dealTalkUrl);//钉钉发送消息的url
            messageArg.put("type", "PUT");
            messageArg.put("header", gson.toJson(dingTalkToken));
            messageArg.put("token", mappingEnterprise.getToken());
            messageArg.put("data", dingTalk);
            String clientUrl = DingRequestUtil.appendProxyHttpUrl(mappingEnterprise.getClientIp());
            Map<String, String> headers = new HashMap<>();
            headers.put("tenantId", String.valueOf(var1.getEi()));
            messageResult = DingRetryHandlerFromProvider.sendOkHttp3Post(clientUrl, headers, gson.toJson(messageArg));
            if (messageResult == null) {
                log.info("externalOaTodoServiceImpl.dealTodo.messageResult={}", messageResult);
                return result;
            }
            JSONObject msgJsonObject = JSONObject.parseObject(messageResult.getContent());
            code = (Integer) msgJsonObject.get("errorCode");
            content = msgJsonObject.get("data").toString();
        } else {
            dingTalkToken.put("tenantId", String.valueOf(var1.getEi()));
            messageResult = DingRetryHandlerFromProvider.sendOkHttp3Put(dealTalkUrl, dingTalkToken, gson.toJson(dingTalk));
            if (messageResult == null) {
                log.info("externalOaTodoServiceImpl.dealTodo.messageResult={}", messageResult);
                return result;
            }
            code = messageResult.getStatusCode();
            content = messageResult.getContent();
        }
        log.info("externalOaTodoServiceImpl.dealTodo,dealTalkUrl={},dingTalkToken={},dingTalk={},messageResult={}", dealTalkUrl, dingTalkToken, dingTalk, messageResult);
        jsonObject = JSONObject.parseObject(content);
        if (code == 200) {
            String dealResult = String.valueOf(jsonObject.get("result"));
            if ("true".equals(dealResult)) {
                //状态设为0
                int sum = 0;
                for (DingTaskVo dingTaskVo : dingTaskVos) {
                    if (operators.contains(dingTaskVo.getEmployeeId())) {
                        int count = dingTodoManager.updateStatus(0, var1.getEi(), var1.getSourceId(), dingTaskVo.getEmployeeId(), appId);
                        log.info("externalOaTodoServiceImpl.dealTodo updateStatus dingTaskVo:{} count:{}", dingTaskVo, count);
                        //更换接口后，就算全部人完成了代办，仍然显示为进行中的代办，需要查询数据库，如果状态全部为0的话，证明代办完成了，调用完成接口
                        sum += count;
                    }
                }
                List<DingTaskVo> dingTaskVos1 = dingTodoManager.getDingTaskVo(var1.getEi(), var1.getSourceId(), appId);
                if (CollectionUtils.isEmpty(dingTaskVos1)) {
                    log.info("externalOaTodoServiceImpl.dealTodo,ea={},thread sleep1.", var1.getEa());
                    //为避免此时有新增待办，调用钉钉接口成功，但是还没有入库导致完成的问题，让当前线程睡会
                    try {
                        Thread.sleep(1000L);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    dingTaskVos1 = dingTodoManager.getDingTaskVo(var1.getEi(), var1.getSourceId(), appId);
                    if (CollectionUtils.isEmpty(dingTaskVos1)) {
                        log.info("externalOaTodoServiceImpl.dealTodo,ea={},Todo is complete.", var1.getEa());
                        //调用完成的接口
                        Boolean isQPSLimit1 = qpsLimitHandlerFromProvider.isQPSLimitByEa(var1.getEa());
                        if (isQPSLimit1) {
                            //限流且重试多次失败
                            log.info("externalOaTodoServiceImpl.dealTodo.qps limit.var2={}", var1);
                            return result;
                        }
                        this.updateTodoStatus(mappingEnterprise, operatorUnionId, dingTaskVos.get(0).getTaskId(), dingTalkToken, appId);
                    }
                }
                log.info("externalOaTodoServiceImpl.dealTodo,ea={},operators={},sum={}", var1.getEa(), operators, sum);
            }
        }
        return result;
    }

    public void updateTodoStatus(DingEnterpriseResult dingEnterpriseResult, String operator, String taskId, Map<String, String> dingTalkToken, String appId) {
        Gson gson = new Gson();
        String updateToDo = ConvertUrlUtils.convertDingUrl(DingUrl.DINGTALK_TODO, dingEnterpriseResult.getNginxIp()) + operator + "/tasks/" + taskId + "?operatorId=" + operator;
        Map<String, Object> dingTalk = Maps.newHashMap();
        boolean flag = true;
        dingTalk.put("done", flag);
        HttpResponseMessage updateToDoMessageResult = null;
        JSONObject jsonObject = null;
        Integer code = null;
        String content = null;
        if (dingEnterpriseResult.getClientIp() != null) {
            Map<String, Object> messageArg = new HashMap<>();
            messageArg.put("url", updateToDo);//钉钉发送消息的url
            messageArg.put("type", "PUT");
            messageArg.put("header", gson.toJson(dingTalkToken));
            messageArg.put("token", dingEnterpriseResult.getToken());
            messageArg.put("data", dingTalk);
            String clientUrl = DingRequestUtil.appendProxyHttpUrl(dingEnterpriseResult.getClientIp());
            Map<String, String> headers = new HashMap<>();
            headers.put("tenantId", String.valueOf(dingEnterpriseResult.getEi()));
            updateToDoMessageResult = DingRetryHandlerFromProvider.sendOkHttp3Post(clientUrl, headers, gson.toJson(messageArg));
            if (updateToDoMessageResult == null) {
                log.info("externalOaTodoServiceImpl.updateTodoStatus.updateToDoMessageResult={}", updateToDoMessageResult);
                return;
            }
            JSONObject msgJsonObject = JSONObject.parseObject(updateToDoMessageResult.getContent());
            code = (Integer) msgJsonObject.get("errorCode");
            content = msgJsonObject.get("data").toString();
        } else {
            dingTalkToken.put("tenantId", String.valueOf(dingEnterpriseResult.getEi()));
            updateToDoMessageResult = DingRetryHandlerFromProvider.sendOkHttp3Put(updateToDo, dingTalkToken, gson.toJson(dingTalk));
            if (updateToDoMessageResult == null) {
                log.info("externalOaTodoServiceImpl.updateTodoStatus.updateToDoMessageResult={}", updateToDoMessageResult);
                return;
            }
            code = updateToDoMessageResult.getStatusCode();
            content = updateToDoMessageResult.getContent();
        }
        jsonObject = JSONObject.parseObject(content);
        if (code == 200) {
            String dealResult = String.valueOf(jsonObject.get("result"));
            if ("true".equals(dealResult)) {
                log.info("externalOaTodoServiceImpl.updateTodoStatus,operator={},taskId={}", operator, taskId);
            }
        }
    }

    public DeleteTodoResult deleteTodo(DeleteTodoArg var1, DingEnterpriseResult mappingEnterprise) {
        //删除代办
        log.info("ExternalOaTodoServiceImpl.deleteTodo: deleteToDO messageArg:{}", var1);
        DeleteTodoResult result = new DeleteTodoResult();
        result.setCode(200);
//        Result<DingEnterpriseResult> mappingEnterprise = dingEnterpriseManager.queryEnterpriseInfoByEi(var1.getEi());
//        if (Objects.isNull(mappingEnterprise) || Objects.isNull(mappingEnterprise.getData())) {
//            log.warn("ei={}.", var1.getEi());
//            return result;
//        }
//        if(!(mappingEnterprise.getData().getAlertStatus().equals(AlertStatusEnum.TODO_STATUS.getStatus()) || mappingEnterprise.getData().getAlertStatus().equals(AlertStatusEnum.TODO_AND_REMIND_STATUS.getStatus()))){
//            List<DingTaskVo> dingTaskVo = dingTodoManager.getDingTaskVo(var1.getEi(), var1.getSourceId());
//            if(CollectionUtils.isEmpty(dingTaskVo)) {
//                //已经关闭就不再发送crm待办消息
//                log.info("externalOaTodoServiceImpl.createTodo,crm alert status was closed arg:{}",var1);
//                result.setMessage("crm alert was closed");
//                return result;
//            }
//        }

        //去掉crm与钉钉无绑定的人员的账号
        List<Integer> deleteEmployeeIds = new LinkedList<>();
        final String appId = mappingEnterprise.getAppKey();
        for (int i = 0; i < var1.getDeleteEmployeeIds().size(); i++) {
            Boolean isBind = dingMappingEmployeeManager.checkEmpBindByEmpId(var1.getEi(), var1.getDeleteEmployeeIds().get(i), appId);
            if (isBind) {
                deleteEmployeeIds.add(var1.getDeleteEmployeeIds().get(i));
            }
        }
        if (CollectionUtils.isEmpty(deleteEmployeeIds)) {
            log.info("the receivers is not binded.");
            return result;
        }
        log.info("externalOaTodoServiceImpl.deleteTodo.ea={},receiverIds={}.", var1.getEa(), deleteEmployeeIds);
//        Boolean isQPSLimit = qpsLimitHandler.isQPSLimitByEa(var1.getEa());
//        if(isQPSLimit) {
//            //限流且重试多次失败
//            log.info("externalOaTodoServiceImpl.deleteTodo.qps limit.var1={}", var1);
//            return result;
//        }
        String accessToken = tokenManager.getToken(var1.getEi(), mappingEnterprise.getAppKey());
        Map<String, String> dingTalkToken = new HashMap<>();
        dingTalkToken.put("x-acs-dingtalk-access-token", accessToken);
        //走更新
        List<DingTaskVo> dingTaskVo = dingTodoManager.getDingTaskVo(var1.getEi(), var1.getSourceId(), appId);
        if (CollectionUtils.isEmpty(dingTaskVo)) {
            log.info("externalOaTodoServiceImpl.deleteTodo.receiverIds is null");
            return result;
        }
        List<DingMappingEmployeeResult> employeeLists = ListUtils.emptyIfNull(dingMappingEmployeeManager.batchGetDingEmployeesByFsIds(var1.getEi(), deleteEmployeeIds, appId).getData()).stream().filter(employee -> {
            final boolean empty = StringUtils.isEmpty(employee.getDingUnionId());
            if (empty) {
                log.info("userUnionId is null employee:{}", employee);
            }
            return !empty;
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(employeeLists)) {
            log.info("externalOaTodoServiceImpl.deleteTodo.employeeLists is null.");
            return result;
        }
        List<String> userUnionIds = employeeLists.stream().map(DingMappingEmployeeResult::getDingUnionId).collect(Collectors.toList());
        int operatorId = employeeLists.get(0).getEmployeeId();
        String operatorUnionId = employeeLists.get(0).getDingUnionId();
        log.info("externalOaTodoServiceImpl.deleteTodo.ea={},userUnionIds={}.", var1.getEa(), userUnionIds);

        Map<String, Object> dingTalk = Maps.newHashMap();
        String dealTalkUrl = ConvertUrlUtils.convertDingUrl(DingUrl.DINGTALK_TODO, mappingEnterprise.getNginxIp()) + operatorUnionId + "/tasks/sources/" + var1.getSourceId();
        //调用第三方接口
        Gson gson = new Gson();
        Boolean isQPSLimit3 = qpsLimitHandlerFromProvider.isQPSLimitByEa(var1.getEa());
        if (isQPSLimit3) {
            //限流且重试多次失败
            log.info("externalOaTodoServiceImpl.deleteTodo.qps limit.var1={}", var1);
            return result;
        }

        try {
            for (int crmUserId : deleteEmployeeIds) {//按照Userid分开上报是为了能快速识别哪些userid用了最多的API
                DingAPICallNumer dumpLog = DingAPICallNumer.builder().tenantId(String.valueOf(var1.getEi()))
                        .bizName(var1.getBizType()).title("").senderId(0).appid(appId).crmUserId(crmUserId)
                        .objectApiName(var1.getExtraDataMap().get("objectApiName")).objectId(var1.getExtraDataMap().get("objectId"))
                        .functionName("deleteTodo")
                        .build();
                BizLogClient.send("generic-biz-log", ProtostuffUtil.serializer(dumpLog));
            }
        } catch (Exception ex) {
        }

        HttpResponseMessage messageResult = null;
        JSONObject jsonObject = null;
        Integer code = null;
        String content = null;
        if (mappingEnterprise.getClientIp() != null) {
            Map<String, Object> messageArg = new HashMap<>();
            messageArg.put("url", dealTalkUrl);//钉钉发送消息的url
            messageArg.put("type", "GET");
            messageArg.put("header", gson.toJson(dingTalkToken));
            messageArg.put("token", mappingEnterprise.getToken());
            String clientUrl = DingRequestUtil.appendProxyHttpUrl(mappingEnterprise.getClientIp());
            Map<String, String> headers = new HashMap<>();
            headers.put("tenantId", String.valueOf(var1.getEi()));
            messageResult = DingRetryHandlerFromProvider.sendOkHttp3Post(clientUrl, headers, gson.toJson(messageArg));
            if (messageResult == null) {
                log.info("externalOaTodoServiceImpl.deleteTodo.messageResult is null.");
                return result;
            }
            JSONObject msgJsonObject = JSONObject.parseObject(messageResult.getContent());
            code = (Integer) msgJsonObject.get("errorCode");
            content = msgJsonObject.get("data").toString();
        } else {
            dingTalkToken.put("tenantId", String.valueOf(var1.getEi()));
            messageResult = DingRetryHandlerFromProvider.sendOkHttp3Get(dealTalkUrl, dingTalkToken, new HashMap<>());
            if (messageResult == null) {
                log.info("externalOaTodoServiceImpl.deleteTodo.messageResult is null.");
                return result;
            }
            code = messageResult.getStatusCode();
            content = messageResult.getContent();
        }
        log.info("externalOaTodoServiceImpl.deleteTodo.ea={},dealTalkUrl={},messageResult={}.", var1.getEa(), dealTalkUrl, messageResult);
        jsonObject = JSONObject.parseObject(content);
        if (code == 200) {
            String talkId = String.valueOf(jsonObject.get("id"));
            String executorIds = JSONArray.toJSONString(jsonObject.get("executorIds"));

            // 将json字符串转换为list集合
            List<String> cardVos = new LinkedList<>(JSONArray.parseArray(executorIds, String.class));
            for (int i = 0; i < cardVos.size(); i++) {
                cardVos.removeAll(userUnionIds);
            }
            String participantIds = JSONArray.toJSONString(jsonObject.get("participantIds"));
            // 将json字符串转换为list集合
            List<String> cardVos1 = new LinkedList<>(JSONArray.parseArray(participantIds, String.class));
            for (int i = 0; i < cardVos1.size(); i++) {
                cardVos1.removeAll(userUnionIds);
            }
            Boolean flag = false;
            dingTalk.put("done", flag);
            dingTalk.put("executorIds", cardVos);
            dingTalk.put("participantIds", cardVos1);
            String updateToDo = ConvertUrlUtils.convertDingUrl(DingUrl.DINGTALK_TODO, mappingEnterprise.getNginxIp()) + operatorUnionId + "/tasks/" + talkId + "?operatorId=" + operatorUnionId;
            Boolean isQPSLimit1 = qpsLimitHandlerFromProvider.isQPSLimitByEa(var1.getEa());
            if (isQPSLimit1) {
                //限流且重试多次失败
                log.info("externalOaTodoServiceImpl.deleteTodo.qps limit.var2={}", var1);
                return result;
            }
            HttpResponseMessage updateToDoMessageResult = null;
            Integer updateCode = null;
            if (mappingEnterprise.getClientIp() != null) {
                Map<String, Object> messageArg = new HashMap<>();
                messageArg.put("url", updateToDo);//钉钉发送消息的url
                messageArg.put("type", "PUT");
                messageArg.put("header", gson.toJson(dingTalkToken));
                messageArg.put("token", mappingEnterprise.getToken());
                messageArg.put("data", dingTalk);
                String clientUrl = DingRequestUtil.appendProxyHttpUrl(mappingEnterprise.getClientIp());
                Map<String, String> headers = new HashMap<>();
                headers.put("tenantId", String.valueOf(var1.getEi()));
                updateToDoMessageResult = DingRetryHandlerFromProvider.sendOkHttp3Post(clientUrl, headers, gson.toJson(messageArg));
                if (updateToDoMessageResult == null) {
                    log.info("externalOaTodoServiceImpl.deleteTodo.updateToDoMessageResult is null.");
                    return result;
                }
                JSONObject msgJsonObject = JSONObject.parseObject(updateToDoMessageResult.getContent());
                updateCode = (Integer) msgJsonObject.get("errorCode");
            } else {
                dingTalkToken.put("tenantId", String.valueOf(var1.getEi()));
                updateToDoMessageResult = DingRetryHandlerFromProvider.sendOkHttp3Put(updateToDo, dingTalkToken, gson.toJson(dingTalk));
                if (updateToDoMessageResult == null) {
                    log.info("externalOaTodoServiceImpl.deleteTodo.updateToDoMessageResult is null.");
                    return result;
                }
                updateCode = updateToDoMessageResult.getStatusCode();
            }
            log.info("externalOaTodoServiceImpl.deleteTodo.ea={},dingTalk={},updateToDo={},updateToDoMessageResult={}.", var1.getEa(), dingTalk, updateToDo, updateToDoMessageResult);
            if (updateCode == 200) {
                int count = dingTodoManager.updateExecutor(deleteEmployeeIds, var1.getEi(), var1.getSourceId(), appId);
                log.warn("externalOaTodoServiceImpl.deleteTodo: 更新代办成功，deleteEmployeeId={}，count={}，更新代办成功.", operatorId, count);
                //增加逻辑，客户删除了代办任务，把状态设置为完成
                List<DingTaskVo> dingTaskVo1 = dingTodoManager.getDingTaskVo(var1.getEi(), var1.getSourceId(), appId);
                if (CollectionUtils.isEmpty(dingTaskVo1)) {
                    //调用完成的接口
                    Boolean isQPSLimit2 = qpsLimitHandlerFromProvider.isQPSLimitByEa(var1.getEa());
                    if (isQPSLimit2) {
                        //限流且重试多次失败
                        log.info("externalOaTodoServiceImpl.deleteTodo.qps limit.var3={}", var1);
                        return result;
                    }
                    this.updateTodoStatus(mappingEnterprise, operatorUnionId, talkId, dingTalkToken, appId);
                }
            } else {
                log.warn("ExternalOaTodoServiceImpl.deleteTodo: 更新代办失败，messageArg={}，userId={}.", dingTalk, deleteEmployeeIds);
            }
        }
        return result;
    }
//
//    public void updateTaskStatus(Integer ei, String sourceId, String appId) {
//        List<DingTaskVo> taskIdByDateTime = null;
//        if (StringUtils.isEmpty(sourceId)) {
//            String startTime = "2025-01-03 23:30:00";
//            String endTime = "2025-01-07 23:30:00";
//            List<Integer> byEaTime = null;
//            if (ei == null) {
//                Timestamp enterPriseTime = Timestamp.valueOf("2024-11-01 00:00:00");
//                byEaTime = dingEnterpriseDao.findByEaTime(enterPriseTime);
//            } else {
//                byEaTime = Lists.newArrayList(ei);
//            }
//            for (Integer tenantId : byEaTime) {
//                Timestamp timestamp = Timestamp.valueOf(startTime);
//                Timestamp endTimestamp = Timestamp.valueOf(endTime);
//                taskIdByDateTime = dingTaskDao.getTaskIdByDateTime(tenantId, timestamp, endTimestamp);
//
//            }
//        } else {
//            taskIdByDateTime = dingTaskDao.getTaskIdByCompleteTime(ei, sourceId);
//        }
//        for (DingTaskVo dingTaskVo : taskIdByDateTime) {
//            try {
//                extracted(dingTaskVo,);
//                Thread.sleep(1000);
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        }
//    }
//
//    private void extracted(DingTaskVo dingTaskVo) {
//        String accessToken = tokenManager.getToken(String.valueOf(dingTaskVo.getEi()), );
//        Integer operators = dingTaskVo.getEmployeeId();
//        Map<String, String> dingTalkToken = new HashMap<>();
//        Gson gson = new Gson();
//        dingTalkToken.put("x-acs-dingtalk-access-token", accessToken);
//        List<DingMappingEmployeeResult> employeeLists = dingMappingEmployeeDao.batchGetDingEmployeesByFsIds(dingTaskVo.getEi(), Lists.newArrayList(operators));
//        Result<DingEnterpriseResult> dingEnterpriseResultResult = dingEnterpriseManager.queryEnterpriseByEi(dingTaskVo.getEi());
//        DingEnterpriseResult mappingEnterprise = dingEnterpriseResultResult.getData();
//        if (CollectionUtils.isEmpty(employeeLists)) {
//            log.info("the employeeLists is null.");
//            return;
//        }
//        Map<String, Object> dingTalk = Maps.newHashMap();
//
//        Boolean flag = Boolean.TRUE;
//        dingTalk.put("done", flag);
//        List<Map<String, Object>> executorStatusList = new LinkedList<>();
//        List<String> userUnionIds = employeeLists.stream().map(DingMappingEmployeeResult::getDingUnionId).collect(Collectors.toList());
//        String operatorUnionId = employeeLists.get(0).getDingUnionId();
//        HttpResponseMessage messageResult = null;
//        Integer code = null;
//        String content = null;
//        String updateToDo = DingUrl.DINGTALK_TODO + operatorUnionId + "/tasks/" + dingTaskVo.getTaskId() + "?operatorId=" + operatorUnionId;
//        Map<String, Object> messageArg = new HashMap<>();
//        messageArg.put("url", updateToDo);//钉钉发送消息的url
//        messageArg.put("type", "PUT");
//        messageArg.put("header", gson.toJson(dingTalkToken));
//        messageArg.put("token", mappingEnterprise.getToken());
//        messageArg.put("data", dingTalk);
//        String clientUrl = DingRequestUtil.appendProxyHttpUrl(mappingEnterprise.getClientIp());
//        messageResult = DingRetryHandlerFromProvider.sendOkHttp3Post(clientUrl, new HashMap<>(), gson.toJson(messageArg));
//        JSONObject msgJsonObject = JSONObject.parseObject(messageResult.getContent());
//        code = (Integer) msgJsonObject.get("errorCode");
//        content = msgJsonObject.get("data").toString();
//        log.info("update dataArg:{}:result{}", JSONObject.toJSONString(messageArg), JSONObject.toJSONString(messageResult));
//    }


    public int insertSource(DingTaskVo dingTaskVo, String appId) {
        int update = dingTodoManager.insertSource(dingTaskVo, appId);
        return update;
    }

}
