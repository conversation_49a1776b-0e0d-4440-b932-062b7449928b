<!--<?xml version="1.0" encoding="UTF-8"?>-->
<!--<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"-->
<!--       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"-->
<!--       xmlns="http://www.springframework.org/schema/beans"-->
<!--       xsi:schemaLocation="http://www.springframework.org/schema/beans-->
<!--       http://www.springframework.org/schema/beans/spring-beans.xsd-->
<!--       http://code.alibabatech.com/schema/dubbo-->
<!--       http://code.alibabatech.com/schema/dubbo/dubbo.xsd">-->

<!--    <dubbo:application name="${dubbo.application.name}"/>-->
<!--    <dubbo:registry address="${dubbo.registry.address}" file="${dubbo.registry.file}" timeout="120000"/>-->
<!--    <dubbo:consumer check="false" timeout="15000" filter="tracerpc"/>-->
<!--    <dubbo:reference id="activeSessionAuthorizeService"-->
<!--                     interface="com.facishare.asm.api.service.ActiveSessionAuthorizeService" />-->
<!--    &lt;!&ndash; 开平消息服务 &ndash;&gt;-->
<!--&lt;!&ndash;    <dubbo:reference id="sendMessageService" interface="com.facishare.open.msg.service.SendMessageService" version="1.0" timeout="10000" />&ndash;&gt;-->


<!--</beans>-->