# FS Open Feishu Gateway

## 项目简介
CRM上架飞书，账号对接服务。本项目主要提供组织架构同步及管理功能，实现CRM与飞书之间的组织架构数据互通。该项目支持多种外部渠道对接，包括飞书、企业微信、钉钉等，实现了企业员工数据的统一管理。

## 核心功能
### 1. 组织架构展示与管理
- 树形结构展示部门和人员
- 支持搜索和筛选
- 支持展开/收起操作
- 显示同步状态和错误信息
- 部门与人员信息编辑
- 组织关系调整
- 变更历史记录

### 2. 组织架构同步
- 同步规则配置
- 手动触发同步
- 自动同步设置
- 同步状态监控
- 错误处理和重试机制

### 3. 批量操作
- 批量部门同步
- 批量人员同步
- 批量状态更新

### 4. 外部系统集成
- 飞书集成
- 企业微信集成
- 钉钉集成
- 华为WeLink集成

## 技术架构
- 后端框架：Spring MVC + Spring Boot
- 数据存储：PostgreSQL（主要）+ MySQL
- ORM框架：MyBatis Plus
- 缓存：Redis
- 服务调用：Dubbo
- 构建工具：Maven

## 项目结构
- `oa-base-db-proxy`：数据库代理层，封装了对数据库的操作
- `outer-oa-connector-web`：Web应用层，提供API接口和页面
- `outer-oa-connector-common-api`：公共API模块，定义通用接口和数据结构
- `outer-oa-connector-base`：基础功能模块
- `outer-oa-connector-i18n`：国际化模块
- `fs-feishu-web`：飞书集成模块
- `fs-qywx-web`：企业微信集成模块
- `fs-dingtalk-web`：钉钉集成模块
- `fs-dingtalk-web-self-build-app`：钉钉自建应用模块
- `fs-huawei-kit-web`：华为WeLink集成模块
- `fs-order-contacts-proxy`：订单联系人代理模块
- `aliyun-market-provider`：阿里云市场提供商模块

## 数据模型
### 主要实体
- **OuterOaEmployeeDataEntity**：外部OA人员数据
- **OuterOaEmployeeBindEntity**：外部OA人员绑定关系
- **OuterOaEnterpriseBindEntity**：外部OA企业绑定关系
- **OuterOaConfigInfoEntity**：外部OA配置信息

## 环境配置
### 开发环境
- JDK版本：1.8（推荐使用Zulu JDK 1.8.0_442 或更高版本）
- Tomcat版本：9.0.102
- 默认端口：8087
- 访问路径：http://localhost:8087/outer_oa_connector_web_war/

### 配置文件
- 环境配置：使用`process.profile`和`spring.profiles.active`参数指定环境
- 支持环境：dev（开发）、test（测试）、fstest（飞书测试）、prod（生产）

### 启动参数说明
部署到Tomcat时，可以配置以下JVM参数：
```
-Dprocess.profile=fstest
-Dspring.profiles.active=fstest
-Dprocess.name=outer-oa-connector-web
-Dapp.name=outer-oa-connector-web
```

## 最近更新
### 2024-05-24: 优化AdminInterceptor管理员权限验证拦截器
- 重构了AdminInterceptor管理员权限验证拦截器，使其能够拦截`/admin/**`路径
- 优化了从Cookie获取用户信息的逻辑，提取为独立方法`tryGetUserFromCookie`
- 改进了错误处理机制，使用JSON格式返回错误信息，而不是直接抛出异常
- 增加了详细的日志记录，便于问题排查和性能分析
- 使用XML配置方式（springmvc-servlet.xml）注册拦截器，遵循项目现有的配置风格
- 实现了更友好的错误响应，包含错误码和详细错误信息
- 在请求处理前后清理用户上下文，避免数据泄露和线程污染
- 增加测试环境自动判断功能，在测试环境中自动使用默认用户信息并跳过管理员权限验证
- 支持企业ID为1的系统内部用户自动通过权限验证，便于系统内部调试和开发

### 2024-04-01: 增加使用OuterOaEmployeeDataEntity直接创建员工的方法
- 在`ObjectDataManager`中添加了新版本的`createEmployee`方法，支持直接传入`OuterOaEmployeeDataEntity`对象
- 新方法直接使用提供的`OuterOaEmployeeDataEntity`数据进行员工创建，而不需要再次查询数据库
- 简化了员工创建流程，提高数据处理效率
- 支持更灵活的员工数据处理方式，便于在不同场景下使用

### 2024-04-01: 增加获取正常状态员工绑定信息的查询方法
- 在`OuterOaEmployeeBindManager`中添加了`getNormalStatusEmployees`方法，支持通过渠道、企业EA、员工ID等条件查询正常状态的员工
- 增加了`getNormalStatusEmployeesByParams`方法，提供基于参数对象的查询方式，更灵活
- 这两个方法都会自动过滤出绑定状态为"normal"的记录
- 支持按更新时间降序排序，便于获取最新的数据
- 提高了代码重用性，简化了获取正常状态员工的操作

### 2024-03-29: 完成Tomcat部署配置
- 配置Tomcat 9.0.102作为应用服务器
- 设置应用访问路径为：http://localhost:8087/outer_oa_connector_web_war/
- 配置JVM参数支持fstest环境
- 完善日志配置，使用logback作为日志框架

### 2024-03-27: 新增outer-oa-connector-admin管理后台模块
- 创建了新的管理后台模块，依赖oa-base-db-proxy模块
- 提供了配置信息的查询和保存接口
- 实现了与数据库代理层的集成
- 支持通过REST API进行管理操作
- 基于Spring Boot实现，便于独立部署

### 2024-03-26: 增加外部OA配置信息批量upsert功能
- 在OuterOaConfigInfoMapper中添加了batchUpsertByDcIdAndType方法，支持批量插入或更新
- 使用PostgreSQL的ON CONFLICT语法实现高效的批量upsert操作
- 添加了OuterOaConfigInfoManager.batchUpsertByDcIdAndType方法，支持在业务层调用批量操作
- 自动处理ID生成和时间戳设置
- 以dcId和type作为唯一判断条件，提高数据写入性能，减少数据库负载
- 增强系统的数据处理能力，特别是在大批量数据同步场景下

### 2024-03-26: 增加外部OA配置信息upsert方法
- 在OuterOaConfigInfoManager中添加了upsertByDcIdAndType方法
- 该方法支持根据数据中心ID(dcId)和配置类型(OuterOaConfigInfoTypeEnum)进行更新或插入操作
- 自动处理创建时间和更新时间字段
- 如果记录已存在则更新，不存在则插入新记录
- 增强了数据一致性，简化了业务层代码逻辑

### 2023-07-07: 改进查询未绑定员工的方法
- 优化了`queryUnboundEmployeesByField`方法，支持更灵活的条件查询
- 修改了参数处理逻辑，使用`OuterOaEmployeeDataParams`对象封装查询条件
- 改进了SQL语句结构，支持多个可选条件组合查询
- 更新了相关Manager类的方法调用
- 增强了日志记录，方便问题定位和性能分析

### 2024-03-29: 添加飞书OA连接Dubbo服务
- 创建FeishuOASettingService接口，定义飞书OA连接服务的远程方法
- 使FeishuOuterOaConnectManager实现FeishuOASettingService接口
- 配置fs-feishu-web作为服务提供者（Provider）
- 配置outer-oa-connector-web作为服务消费者（Consumer）
- 创建FeishuOASettingServiceImpl类作为服务封装，简化调用方式

## fs-feishu-web Manager替换规则使用说明

### 背景

fs-feishu-web模块需要进行旧dbmapper以及manager的替换。新的manager在oa-base-db-proxy模块下。

### 映射关系

旧manager与新manager的映射关系如下：

| 旧Manager | 新Manager |
| --- | --- |
| EnterpriseBindManager | OuterOaEnterpriseBindManager |
| EmployeeBindManager | OuterOaEmployeeBindManager |
| DepartmentBindManager | OuterOaDepartmentBindManager |
| OrderInfoManager | OuterOaOrderInfoManager |

### 使用方法

1. 在代码中，当你需要使用旧的manager时，输入旧manager的名称，然后按下Tab键或Enter键
2. Cursor会自动根据规则将旧manager替换为新manager
3. 同时，Cursor会自动处理以下内容：
   - 替换import语句
   - 替换实体类名称
   - 替换方法参数
   - 替换返回值类型
   - 自动导入需要的包

### 注意事项

1. 新旧manager的方法参数不完全一致，规则会自动使用builder模式构造参数
2. 对于getEntity方法，会被替换为getEntities方法，并使用stream().findFirst().orElse(null)获取单个结果
3. 对于getEntityList方法，会被替换为getEntities方法
4. 特殊方法如insertOrUpdateOrderInfo和getLatestOrder会保持不变
5. fs-feishu-web是基于spring-mvc模式开发，在替换manager时不需要new bean

### 示例

旧代码：
```java
@Autowired
private EnterpriseBindManager enterpriseBindManager;

public void someMethod() {
    EnterpriseBindEntity entity = enterpriseBindManager.getEntity(fsEa);
    List<EnterpriseBindEntity> list = enterpriseBindManager.getEntityList(fsEa, bindStatus);
}
```

替换后：
```java
@Autowired
private OuterOaEnterpriseBindManager enterpriseBindManager;

public void someMethod() {
    OuterOaEnterpriseBindEntity entity = enterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().fsEa(fsEa).build()).stream().findFirst().orElse(null);
    List<OuterOaEnterpriseBindEntity> list = enterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().fsEa(fsEa).bindStatus(bindStatus).build());
}
```

### 规则说明

规则文件(.cursorrules)包含以下几类规则：

1. Manager类名替换规则
2. 实体类替换规则
3. 方法参数替换规则
4. 参数构造规则
5. 特殊方法替换规则
6. 自动导入包规则
7. 返回值替换规则

这些规则共同作用，确保在替换过程中代码的正确性和一致性。
