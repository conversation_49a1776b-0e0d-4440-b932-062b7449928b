package com.facishare.open.ding.web.controller;

import com.alibaba.fastjson.JSON;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.result.ResultCode;
import com.facishare.open.ding.template.login.DingtalkLoginTemplate;
import com.facishare.open.ding.template.model.FsUserModel;
import com.facishare.open.ding.web.base.BaseController;
import com.facishare.open.ding.web.base.UserVo;
import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEmployeeBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEmployeeBindManager;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.outer.oa.connector.common.api.enums.BindTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.info.EnterpriseTrialInfo;
import com.facishare.open.outer.oa.connector.common.api.login.UserTicketModel;
import com.facishare.open.outer.oa.connector.common.api.result.ResultCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * Created by system on 2018/4/4.
 */
@Slf4j
@RestController
@RequestMapping("/web")
public class DingWebController extends BaseController{
    @Autowired
    private DingtalkLoginTemplate dingtalkLoginTemplate;
    @Autowired
    private OuterOaEmployeeBindManager outerOaEmployeeBindManager;

    /**
     * 留资功能使用的接口，得到企业的最新订单情况和绑定类型
     * @return
     */
    @RequestMapping(value = "/order/getEnterpriseTrialInfo", method = RequestMethod.POST)
    @ResponseBody
    public Result<EnterpriseTrialInfo> getEnterpriseTrialInfo() {
        UserVo userVo = getUserVo();
        if(ObjectUtils.isEmpty(userVo)) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        //插件直接返回
        EnterpriseTrialInfo info = new EnterpriseTrialInfo();
        info.setBindType(BindTypeEnum.manual);
        return Result.newSuccess(info);
    }

    /**
     * 用ticket获取纷享用户信息，给俊文使用
     *
     * @param ticket
     * @return
     */
    @Deprecated
    @RequestMapping(value="/getFsUserInfo",method = RequestMethod.GET)
    @ResponseBody
    public com.facishare.open.outer.oa.connector.common.api.result.Result<FsUserModel> getFsUserInfo(@RequestParam String ticket) {
        LogUtils.info("DingWebController.getFsUserInfo,ticket={}",ticket);
        MethodContext context = MethodContext.newInstance(ticket);
        dingtalkLoginTemplate.getFsUserInfoByTicket(context);
        com.facishare.open.outer.oa.connector.common.api.result.Result<FsUserModel> fsUser = context.getResultData();
        LogUtils.info("DingWebController.getFsUserInfo,fsUser={}",fsUser);
        return fsUser;
    }

    /**
     * 用ticket获取纷享用户信息，灰度使用
     */
    @Deprecated
    @RequestMapping(value = "/getFsUserInfo2", method = RequestMethod.GET)
    @ResponseBody
    public com.facishare.open.outer.oa.connector.common.api.result.Result<FsUserModel> getFsUserInfo2(@RequestParam("ticketModel") String ticketModelStr) {
        UserTicketModel ticketModel = JSON.parseObject(ticketModelStr, UserTicketModel.class);
        final String fsEa = ticketModel.getFsEa();
        final OuterOaEmployeeBindEntity outerOaEmployeeBindEntity = outerOaEmployeeBindManager.queryNormalByFsEaAndOutEmpId(ChannelEnum.dingding, fsEa, ticketModel.getAppId(), ticketModel.getOuterUserId());
        log.info("DingLoginServiceImpl.getFsUser,employeeMapping={}", outerOaEmployeeBindEntity);
        if (ObjectUtils.isEmpty(outerOaEmployeeBindEntity)) {
            return com.facishare.open.outer.oa.connector.common.api.result.Result.newError(ResultCodeEnum.EMPLOYEE_NO_BIND_INFO);
        }
        FsUserModel fsUserModel = new FsUserModel(fsEa, outerOaEmployeeBindEntity.getFsEmpId(), ticketModel.getAppId());
        log.info("DingLoginServiceImpl.getFsUser,fsUserModel={}", fsUserModel);
        return com.facishare.open.outer.oa.connector.common.api.result.Result.newSuccess(fsUserModel);
    }
}
