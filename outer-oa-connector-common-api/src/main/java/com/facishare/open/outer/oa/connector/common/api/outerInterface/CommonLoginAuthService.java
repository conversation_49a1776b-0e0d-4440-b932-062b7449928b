package com.facishare.open.outer.oa.connector.common.api.outerInterface;

import com.facishare.open.outer.oa.connector.common.api.login.CrmUserModel;
import com.facishare.open.outer.oa.connector.common.api.login.UserTicketModel;
import com.facishare.open.outer.oa.connector.common.api.result.Result;

/**
 * 提供给俊文app-view使用的dubbo-reset接口
 */
public interface CommonLoginAuthService {

    Result<UserTicketModel> getCrmUserByTicket(String ticket,String channel);
}
