package com.facishare.open.feishu.web.template.outer.event.app;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.erpdss.outer.oa.connector.base.outer.event.app.AppEventTemplate;
import com.facishare.open.feishu.syncapi.config.ConfigCenter;
import com.facishare.open.feishu.syncapi.entity.AppInfoEntity;
import com.facishare.open.feishu.syncapi.enums.AppStatusEnum;
import com.facishare.open.feishu.syncapi.enums.MemberTypeEnum;
import com.facishare.open.feishu.syncapi.model.connect.AppConnectParams;
import com.facishare.open.feishu.syncapi.model.event.FeishuAppOpenEvent;
import com.facishare.open.feishu.syncapi.model.event.FeishuAppStatusChangeEvent;
import com.facishare.open.feishu.syncapi.model.event2.FeishuContactScopeUpdateV3Event;
import com.facishare.open.feishu.syncapi.model.event2.FeishuEventModel2;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.data.DepartmentData;
import com.facishare.open.feishu.syncapi.result.data.UserData;
import com.facishare.open.feishu.syncapi.result.data.UserGroupData;
import com.facishare.open.feishu.syncapi.result.data.UserGroupMemberData;
import com.facishare.open.feishu.syncapi.service.*;
import com.facishare.open.feishu.web.handler.FeishuEventHandler;
import com.facishare.open.feishu.web.template.model.FeishuEventHandleModel;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaAppInfoEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaConfigInfoEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaAppInfoManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaConfigInfoManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaOrderInfoManager;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaAppInfoParams;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEnterpriseBindParams;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.outer.oa.connector.common.api.enums.*;
import com.facishare.open.outer.oa.connector.common.api.info.SettingAccountRulesModel;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 飞书应用事件处理器
 * <AUTHOR>
 * @date 2024-08-20
 */

@Component
public class FeishuAppEventTemplate extends AppEventTemplate {
    @Resource
    private AppService appService;
    @Resource
    private CorpService corpService;
    @Resource
    private ContactsService contactsService;
    @Resource
    private EnterpriseBindService enterpriseBindService;
    @Resource
    private FeishuUserGroupService feishuUserGroupService;
    @Resource
    private FeishuUserService feishuUserService;
    @Resource
    private FeishuDepartmentService feishuDepartmentService;
    @Autowired
    private OuterOaAppInfoManager outerOaAppInfoManager;
    @Autowired
    private OuterOaOrderInfoManager outerOaOrderInfoManager;
    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Autowired
    private OuterOaConfigInfoManager outerOaConfigInfoManager;

    @Override
    public void onAppOpen(MethodContext context) {
        LogUtils.info("FeishuAppEventTemplate.onAppOpen,context={}",context);
        FeishuAppOpenEvent event = context.getData();
        AppConnectParams appConnectParams = AppConnectParams.builder()
                .appId(event.getAppId())
                .applicants(JSONObject.toJSONString(event.getApplicants()))
                .installerOpenId(event.getInstaller()!=null ? event.getInstaller().getOpenId() : null)
                .installerEmployeeOpenId(event.getInstallerEmployee()!=null ? event.getInstallerEmployee().getOpenId() : null)
                .build();
        ChannelEnum channelEnum=ChannelEnum.valueOf(ConfigCenter.CURRENT_CHANNEL_ISV_CHANNEL);
        OuterOaAppInfoEntity outerOaAppInfoEntity = OuterOaAppInfoEntity.builder()
                .outEa(event.getTenantKey())
                .appId(event.getAppId())
                .appType(OuterOaAppInfoTypeEnum.isv)
                .channel(channelEnum)
                .status(OuterOaAppInfoStatusEnum.normal)
                .appInfo(JSONObject.toJSONString(appConnectParams)).createTime(System.currentTimeMillis()).updateTime(System.currentTimeMillis()).build();
        Integer insertCount = outerOaAppInfoManager.upsert(outerOaAppInfoEntity);
        context.setResult(TemplateResult.newSuccess(FeishuEventHandler.SUCCESS));
    }

    @Override
    public void onAppStatusChange(MethodContext context) {
        LogUtils.info("FeishuAppEventTemplate.onAppStatusChange,context={}",context);
        FeishuAppStatusChangeEvent event = context.getData();
        //查询应用信息
        OuterOaAppInfoParams outerOaAppInfoParams=OuterOaAppInfoParams.builder()
                .outEa(event.getTenantKey())
                .appId(event.getAppId())
                .build();
        List<OuterOaAppInfoEntity> entities = outerOaAppInfoManager.getEntities(outerOaAppInfoParams);
        if(CollectionUtils.isEmpty(entities)){
            return;
        }
        // AppStatusEnum status;  //应用状态 start_by_tenant: 租户启用; stop_by_tenant: 租户停用; stop_by_platform: 平台停用
        for (OuterOaAppInfoEntity outerOaAppInfoEntity : entities) {
            if(event.getStatus()==AppStatusEnum.start_by_tenant) {
                outerOaAppInfoEntity.setStatus(OuterOaAppInfoStatusEnum.normal);
            } else if(event.getStatus()==AppStatusEnum.stop_by_tenant) {
                outerOaAppInfoEntity.setStatus(OuterOaAppInfoStatusEnum.stop);
            } else if(event.getStatus()==AppStatusEnum.stop_by_platform) {
                outerOaAppInfoEntity.setStatus(OuterOaAppInfoStatusEnum.stop);
            }
            outerOaAppInfoManager.updateById(outerOaAppInfoEntity);
        }
        context.setResult(TemplateResult.newSuccess(FeishuEventHandler.SUCCESS));
    }

    @Override
    public void onAppVisibleRangeChange(MethodContext context) {
        LogUtils.info("FeishuAppEventTemplate.onAppVisibleRangeChange,context={}",context);
        FeishuEventHandleModel eventHandleModel = context.getData();
        FeishuEventModel2.EventModelHeader header = eventHandleModel.getHeader();
        String eventData = eventHandleModel.getEventData();
        OuterOaEnterpriseBindParams outerOaEnterpriseBindParams=OuterOaEnterpriseBindParams.builder()
                .outEa(header.getTenantKey())
                .appId(header.getAppId())
                .build();
        List<OuterOaEnterpriseBindEntity> entities = outerOaEnterpriseBindManager.getEntities(outerOaEnterpriseBindParams);
        if(CollectionUtils.isEmpty(entities)){
            return;
        }
        for (OuterOaEnterpriseBindEntity entity : entities) {
//            OuterOaConfigInfoEntity entityByDataCenterId = outerOaConfigInfoManager.getEntityByDataCenterId(OuterOaConfigInfoTypeEnum.SETTING_BIND_RULES, entity.getId());
//            SettingAccountRulesModel settingAccountRulesModel= JSON.parseObject(entityByDataCenterId.getConfigInfo(), SettingAccountRulesModel.class);
            contactsService.refreshContactScopeDataCacheAsync(header.getEventType(), header.getAppId(),
                    header.getTenantKey(), eventData,entity.getChannel());
            context.setResult(TemplateResult.newError(FeishuEventHandler.SUCCESS));




//            if(settingAccountRulesModel.getBindTypeEnum() == BindTypeEnum.manual||settingAccountRulesModel.getSyncTypeEnum()== EnterpriseConfigAccountSyncTypeEnum.accountBind) {
//                LogUtils.info("FeishuAppEventTemplate.onAppVisibleRangeChange,outEa={},设置绑定规则是手动同步，不支持同步通讯录",header.getTenantKey()); //ignorei18n
//                contactsService.refreshContactScopeDataCacheAsync(header.getEventType(), header.getAppId(),
//                        header.getTenantKey(), eventData,entity.getChannel());
//                context.setResult(TemplateResult.newError(FeishuEventHandler.SUCCESS));
//            }else{
//                FeishuContactScopeUpdateV3Event event = JSON.parseObject(eventData, FeishuContactScopeUpdateV3Event.class);
//                LogUtils.info("FeishuAppEventTemplate.onAppVisibleRangeChange,event={}",event);
//                if(event.getAdded()!=null) {
//                    processAddEvent(header,event.getAdded(),entity);
//                }
//                if(event.getRemoved()!=null) {
//                    processRemoveEvent(header,event.getRemoved(),entity);
//                }
//                context.setResult(TemplateResult.newSuccess(FeishuEventHandler.SUCCESS));
//            }
//            LogUtils.info("FeishuAppEventTemplate.onAppVisibleRangeChange,end,context={}",context);
        }
    }

    @Override
    public void onAppStop(MethodContext context) {
        //暂时不实现
    }

    private void processUserGroup(String appId,
                                  String tenantKey,
                                  List<UserGroupData> userGroupDataList,
                                  List<UserData.User> userList,
                                  List<DepartmentData.Department> departmentList) {
        for(UserGroupData userGroupData : userGroupDataList) {
            String groupId = userGroupData.getUserGroupId();
            Result<List<UserGroupMemberData>> result = feishuUserGroupService.getUserGroupMemberList(appId,
                    tenantKey,
                    groupId,
                    MemberTypeEnum.user);
            LogUtils.info("processUserGroup,getUserGroupMemberList,user,groupId={},result={}",groupId,result);
            if(result.isSuccess() && org.apache.commons.collections4.CollectionUtils.isNotEmpty(result.getData())) {
                List<String> openUserIdList = result.getData().stream()
                        .map(UserGroupMemberData::getMemberId)
                        .collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(openUserIdList)) {
                    for(String openUserId : openUserIdList) {
                        Result<UserData.User> userInfo = feishuUserService.getUserInfo(appId, tenantKey, openUserId);
                        LogUtils.info("processUserGroup,getUserInfo,openUserId={},result={}",openUserId,userInfo);
                        if(userInfo.isSuccess()) {
                            userList.add(userInfo.getData());
                        }
                    }
                }
            }

            result = feishuUserGroupService.getUserGroupMemberList(appId,
                    tenantKey,
                    groupId,
                    MemberTypeEnum.department);
            LogUtils.info("processUserGroup,getUserGroupMemberList,department,groupId={},result={}",groupId,result);
            if(result.isSuccess() && org.apache.commons.collections4.CollectionUtils.isNotEmpty(result.getData())) {
                List<String> openDepIdList = result.getData().stream()
                        .map(UserGroupMemberData::getMemberId)
                        .collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(openDepIdList)) {
                    for(String openDepId : openDepIdList) {
                        Result<DepartmentData.Department> deptInfo = feishuDepartmentService.getDeptInfo(appId, tenantKey, openDepId);
                        LogUtils.info("processUserGroup,getDeptInfo,openDepId={},result={}",openDepId,deptInfo);
                        if(deptInfo.isSuccess()) {
                            departmentList.add(deptInfo.getData());
                        }
                    }
                }
            }
        }
    }

    private void processAddEvent(FeishuEventModel2.EventModelHeader header, FeishuContactScopeUpdateV3Event.Added added,OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity) {
        if(CollectionUtils.isNotEmpty(added.getUserGroups())) {
            LogUtils.info("processAddEvent,userGroups={}",added.getUserGroups());
            String appId = header.getAppId();
            String tenantKey = header.getTenantKey();
            if(CollectionUtils.isEmpty(added.getUsers())) {
                added.setUsers(new ArrayList<>());
            }
            if(CollectionUtils.isEmpty(added.getDepartments())) {
                added.setDepartments(new ArrayList<>());
            }
            processUserGroup(appId, tenantKey, added.getUserGroups(), added.getUsers(), added.getDepartments());
            LogUtils.info("processAddEvent,added={}",added);
        }
        //应用可见范围新增用户处理逻辑
        if(CollectionUtils.isNotEmpty(added.getUsers())) {
            Result<Void> result = contactsService.addUserList(header.getAppId(),outerOaEnterpriseBindEntity,header.getTenantKey(),added.getUsers());
            LogUtils.info("processAddEvent,add user list,result={}",result);
        }
        //应用可见范围新增部门处理逻辑
        if(CollectionUtils.isNotEmpty(added.getDepartments())) {
            Result<Void> result = contactsService.addDepList(header.getAppId(),header.getTenantKey(),added.getDepartments(),outerOaEnterpriseBindEntity);
            LogUtils.info("processAddEvent,add department list,result={}",result);
        }
    }

    private void processRemoveEvent(FeishuEventModel2.EventModelHeader header,FeishuContactScopeUpdateV3Event.Removed removed,OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity) {
        if(CollectionUtils.isNotEmpty(removed.getUserGroups())) {
            LogUtils.info("processRemoveEvent,userGroups={}",removed.getUserGroups());
            String appId = header.getAppId();
            String tenantKey = header.getTenantKey();
            if(CollectionUtils.isEmpty(removed.getUsers())) {
                removed.setUsers(new ArrayList<>());
            }
            if(CollectionUtils.isEmpty(removed.getDepartments())) {
                removed.setDepartments(new ArrayList<>());
            }
            processUserGroup(appId, tenantKey, removed.getUserGroups(), removed.getUsers(), removed.getDepartments());
            LogUtils.info("processRemoveEvent,removed={}",removed);
        }
        //应用可见范围移除用户处理逻辑
        if(CollectionUtils.isNotEmpty(removed.getUsers())) {
            Result<Void> result = contactsService.removeRangeUserList(header.getAppId(),header.getTenantKey(),removed.getUsers(),outerOaEnterpriseBindEntity);
            LogUtils.info("processRemoveEvent,remove user list,result={}",result);
        }
        //应用可见范围移除部门处理逻辑
        if(CollectionUtils.isNotEmpty(removed.getDepartments())) {
            Result<Void> result = contactsService.removeDepList(header.getAppId(),header.getTenantKey(),removed.getDepartments(),outerOaEnterpriseBindEntity);
            LogUtils.info("processRemoveEvent,remove department list,result={}",result);
        }
    }
}
