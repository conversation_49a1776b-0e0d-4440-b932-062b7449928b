package com.facishare.open.qywx.old.accountsync.test;

import com.facishare.open.qywx.messagesend.enums.QyWeixinMsgType;
import com.facishare.open.qywx.messagesend.model.InvalidUserResult;
import com.facishare.open.qywx.messagesend.model.SendQyWeixinMsgRsp;
import com.facishare.open.qywx.messagesend.model.TextMsgContent;
import com.facishare.open.qywx.messagesend.result.Result;
import com.facishare.open.qywx.messagesend.service.QYWeixinMessageSendService;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.AbstractJUnit4SpringContextTests;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.List;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
public class QYWeixinMessageSendServiceTest extends AbstractJUnit4SpringContextTests {
    @Autowired
    private QYWeixinMessageSendService qyWeixinMessageSendService;

    @Test
    public void sendMsg2DownStream(){
        String corpId="wwd7cf0bbe8b2c47bd";
        String appId="dk34d6bacd61e33d99";
        QyWeixinMsgType msgType=QyWeixinMsgType.MSG_TEXT;
        TextMsgContent msgContent=new TextMsgContent();
        msgContent.setContent("东东，测试消息111");
        List<String> outUserIdList=Lists.newArrayList("wpwx1mDAAALaUfXVSS8OZOE0Tvj001fg/wowx1mDAAA433N0sj6tL5TsGOQa3nxHw","wowx1mDAAA433N0sj6tL5TsGOQa3noHw");
        Result<InvalidUserResult> result=qyWeixinMessageSendService.sendMsg2DownStream(corpId,appId,msgType,msgContent,outUserIdList);
        outUserIdList=Lists.newArrayList("wpwx1mDAAALaUfXVSS8OZOE0Tvj001fg/wowx1mDAAA433N0sj6tL5TsGOQa3nxHw","wowx1mDAAAJrLOa5TBZiIObCEDaCmZdg");
        Result<InvalidUserResult> result1=qyWeixinMessageSendService.sendMsg2DownStream(corpId,appId,msgType,msgContent,outUserIdList);
        System.out.println("");
    }
}
