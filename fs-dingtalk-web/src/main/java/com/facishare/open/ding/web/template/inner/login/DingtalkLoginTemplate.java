package com.facishare.open.ding.web.template.inner.login;

import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.web.manager.DingtalkApiManager;
import com.facishare.open.ding.web.template.model.FsUserModel;
import com.facishare.open.ding.web.template.model.GetOutUserInfoByCodeArg;
import com.facishare.open.ding.web.template.model.OutUserModel;
import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.erpdss.outer.oa.connector.base.inner.login.LoginTemplate;
import com.facishare.open.outer.oa.connector.common.api.info.ticket.GenFsTicketModel;
import com.facishare.open.outer.oa.connector.common.api.login.UserTicketModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class DingtalkLoginTemplate extends LoginTemplate {
    @Resource
    private DingtalkApiManager dingtalkApiManager;
    @Resource
    private LoginService loginService;

    @Override
    public void getOutUserInfoByCode(MethodContext context) {
        log.info("DingtalkLoginTemplate.getOutUserInfoByCode,context={}", context);
        GetOutUserInfoByCodeArg arg = context.getData();
        Result<OutUserModel> result = dingtalkApiManager.getOutUserInfoByCode(arg);
        log.info("DingtalkLoginTemplate.getOutUserInfoByCode,result={}", result);
        context.setResult(TemplateResult.newSuccess(result));
    }

    @Override
    public void genFsTicket(MethodContext context) {
        log.info("DingtalkLoginTemplate.genFsTicket,context={}", context);

        UserTicketModel fsTicketModel = context.getData();
        com.facishare.open.outer.oa.connector.common.api.result.Result<String> result = loginService.genFsTicket(fsTicketModel);

        log.info("DingtalkLoginTemplate.genFsTicket, result={}", result);
        context.setResult(TemplateResult.newSuccess(result));
    }

    @Override
    public void getFsUserInfoByTicket(MethodContext context) {
        log.info("DingtalkLoginTemplate.getFsUserInfoByTicket,context={}", context);
        String ticket = context.getData().toString();

        com.facishare.open.outer.oa.connector.common.api.result.Result<FsUserModel> result = loginService.getFsUser(ticket);
        log.info("DingtalkLoginTemplate.getFsUserInfoByTicket, result={}", result);

        context.setResult(TemplateResult.newSuccess(result));
    }

    @Override
    public void createUserToken(MethodContext context) {
        super.createUserToken(context);
        //sso token下线
//        log.info("DingtalkLoginTemplate.createUserToken,context={}", context);
//        CreateUserTokenDto.Argument userTokenArg = context.getData();
//        CreateUserTokenDto.Result result = ssoLoginService.createUserToken(userTokenArg);
//        log.info("DingtalkLoginTemplate.createUserToken,result={}", result);
//        context.setResult(TemplateResult.newSuccess(result));
    }
}
