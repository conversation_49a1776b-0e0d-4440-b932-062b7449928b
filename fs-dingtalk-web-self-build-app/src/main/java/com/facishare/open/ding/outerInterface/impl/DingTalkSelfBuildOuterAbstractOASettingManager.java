package com.facishare.open.ding.outerInterface.impl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.ding.common.model.Dept;
import com.facishare.open.ding.common.model.ScopeVo;
import com.facishare.open.ding.common.model.User;
import com.facishare.open.ding.common.utils.BeanUtil;
import com.facishare.open.ding.provider.dao.DingEnterpriseDao;
import com.facishare.open.ding.provider.dingding.DingRequestUtil;
import com.facishare.open.ding.provider.entity.DingEnterprise;
import com.facishare.open.ding.provider.manager.DingMappingEmployeeManager;
import com.facishare.open.ding.provider.manager.TokenManager;
import com.facishare.open.ding.provider.service.EnterpriseServiceImpl;
import com.facishare.open.ding.web.redis.RedisDataSource;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaAppInfoEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaDeptDataEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.dingtalk.OutDeptData;
import com.facishare.open.oa.base.dbproxy.pg.manager.*;
import com.facishare.open.outer.oa.connector.common.api.admin.DingConnectorVo;
import com.facishare.open.outer.oa.connector.common.api.constants.GlobalValue;
import com.facishare.open.outer.oa.connector.common.api.enums.*;
import com.facishare.open.outer.oa.connector.common.api.login.UserTicketModel;
import com.facishare.open.outer.oa.connector.common.api.outerInterface.OuterAbstractSettingService;
import com.facishare.open.outer.oa.connector.common.api.params.DingtalkAppInfoParams;
import com.facishare.open.outer.oa.connector.common.api.result.OuterOAConnectSettingResult;
import com.facishare.open.outer.oa.connector.common.api.result.Result;
import com.facishare.open.outer.oa.connector.common.api.result.ResultCodeEnum;
import com.facishare.uc.api.model.enterprise.arg.GetSimpleEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.GetSimpleEnterpriseDataResult;
import com.facishare.uc.api.model.fscore.SimpleEnterpriseData;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

/**
 * 钉钉自建应用的外部OA设置管理器
 *
 * <AUTHOR>
 * @date 2025/3/27 20:45:16
 */
@Component("dingTalkSelfBuildManager")
@Slf4j
public class DingTalkSelfBuildOuterAbstractOASettingManager implements OuterAbstractSettingService<DingConnectorVo> {

    private final ConcurrentHashMap<String, CompletableFuture<Result<Void>>> taskMap = new ConcurrentHashMap<>();

    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;

    @Autowired
    private OuterOaAppInfoManager outerOaAppInfoManager;

    @Autowired
    private OuterOaDeptDataManager outerOaDeptDataManager;

    @Autowired
    private TokenManager tokenManager;

    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private DingMappingEmployeeManager dingMappingEmployeeManager;
    @Autowired
    private EnterpriseServiceImpl enterpriseService;
    @Autowired
    private EnterpriseEditionService enterpriseEditionService;
    @Autowired
    private OuterOaEmployeeDataManager outerOaEmployeeDataManager;
    @Autowired
    private OuterOaConfigInfoManager outerOaConfigInfoManager;
    @Autowired
    private RedisDataSource redisDataSource;

    @Override
    public Result<Void> doValidateConfigAndSave(OuterOAConnectSettingResult outerOAConnectSettingResult, ChannelEnum channel, OuterOaAppInfoTypeEnum outerOaAppInfoTypeEnum) {
        // 参数校验
        if (outerOAConnectSettingResult == null) {
            log.error("doValidateConfigAndSave failed: connectorVo is null");
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        DingConnectorVo connectorVo = outerOAConnectSettingResult.getConnectParams().getDingding();

        String dataCenterId = outerOAConnectSettingResult.getCurrentDcId();
        connectorVo.setDataCenterId(dataCenterId);
        OuterOaEnterpriseBindEntity entityById = outerOaEnterpriseBindManager.getEntityById(dataCenterId);

        // 检查是否已经有isv的连接器,有的话报错
        final String fsEa = entityById.getFsEa();
        final int ei = eieaConverter.enterpriseAccountToId(fsEa);
        final List<OuterOaEnterpriseBindEntity> entitiesByFsEa = outerOaEnterpriseBindManager.getEntitiesByFsEa(ChannelEnum.dingding, fsEa);
        final String isvAppKey = entitiesByFsEa.stream()
                .map(entity -> JSON.parseObject(entity.getConnectInfo(), DingConnectorVo.class))
                .filter(dingConnectorVo -> Objects.equals(dingConnectorVo.getAppType(), OuterOaAppInfoTypeEnum.isv))
                .findFirst()
                .map(DingConnectorVo::getAppKey)
                .orElse(null);
        if (Objects.nonNull(isvAppKey)) {
            log.error("doValidateConfigAndSave failed: hasIsv ea:{} isvAppKey:{}", fsEa, isvAppKey);
            return Result.newError(ResultCodeEnum.DING_SB_HAS_ISV_BIND);
        }

        // 检查app是否已经有企业绑定,有的话报错
        final String appId = connectorVo.getAppKey();
        final String outEa = connectorVo.getDingCorpId();
        final List<OuterOaEnterpriseBindEntity> entitiesByOutEa = outerOaEnterpriseBindManager.getEntitiesByOutEa(ChannelEnum.dingding, outEa, appId);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(entitiesByOutEa)) {
            final String otherEa = entitiesByOutEa.stream()
                    .filter(entity -> !Objects.equals(entity.getFsEa(), fsEa))
                    .findFirst()
                    .map(OuterOaEnterpriseBindEntity::getFsEa)
                    .orElse(null);
            if (StringUtils.isNotEmpty(otherEa)) {
                log.error("doValidateConfigAndSave failed: hasBindOutEa ea:{} otherEa:{} outEa:{} appId:{}", fsEa, otherEa, outEa, appId);
                return Result.newError(ResultCodeEnum.DING_SB_APP_HAS_BIND);
            }
        }

        //验证appKey和appSecret
        String accessToken = DingRequestUtil.getToken(ei, connectorVo.getClientIp(), appId, connectorVo.getAppSecret(), connectorVo.getNginxIp());
        if (StringUtils.isEmpty(accessToken)) {
            log.warn("startConnect appKey或appSecret错误.");
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }

        // 更新应用信息
        outerOaAppInfoManager.batchUpsertInfos(Lists.newArrayList(convert2AppInfo(connectorVo)));

        // 更新企业绑定信息
        final OuterOaEnterpriseBindEntity copy = BeanUtil.copy(entityById, OuterOaEnterpriseBindEntity.class);
        copy.setOutEa(outEa);
        copy.setAppId(appId);
        copy.setConnectInfo(JSON.toJSONString(connectorVo));
        copy.setBindType(BindTypeEnum.manual);
        copy.setBindStatus(BindStatusEnum.normal);
        copy.setUpdateTime(System.currentTimeMillis());
        Integer count = outerOaEnterpriseBindManager.updateById(copy);
        if (count <= 0) {
            log.warn("saveEnterprise failed, entityById={}.", copy);
            return Result.newSuccess();
        }

        // 初始化人员绑定设置
        final String dcId = outerOaEnterpriseBindManager.getDcIdByEaAndAppId(ChannelEnum.dingding, fsEa, appId);
        final ArrayList<OuterOaConfigInfoTypeEnum> initConfigEnums = Lists.newArrayList(OuterOaConfigInfoTypeEnum.SETTING_BIND_RULES, OuterOaConfigInfoTypeEnum.EMPLOYEE_UNIQUE_IDENTITY, OuterOaConfigInfoTypeEnum.OUTER_SYSTEM_OBJECT_FIELDS);
        for (OuterOaConfigInfoTypeEnum outerOaConfigInfoTypeEnum : initConfigEnums) {
            outerOaConfigInfoManager.getEntityByDataCenterId(outerOaConfigInfoTypeEnum, dcId);
        }

        if (StringUtils.isEmpty(entityById.getAppId())) {
            // 注册回调地址
            enterpriseService.registerDingCallback(ei, fsEa, appId, connectorVo.getAppSecret(), accessToken, connectorVo.getClientIp(), connectorVo.getNginxIp());

            // 蜂眼 企业开通数统计
            enterpriseService.statisEnterpriseBindAccount(ei, fsEa, getEnterpriseName(ei));
        }

        return Result.newSuccess();
    }

    public String getEnterpriseName(Integer ei) {
        final GetSimpleEnterpriseDataResult simpleEnterpriseData = enterpriseEditionService.getSimpleEnterpriseData(new GetSimpleEnterpriseDataArg(ei, null));
        return Optional.ofNullable(simpleEnterpriseData)
                .map(GetSimpleEnterpriseDataResult::getEnterpriseData)
                .map(SimpleEnterpriseData::getEnterpriseName)
                .orElse(null);
    }

    private OuterOaAppInfoEntity convert2AppInfo(DingConnectorVo connectorVo) {
        final OuterOaAppInfoEntity outerOaAppInfoEntity = new OuterOaAppInfoEntity();
        outerOaAppInfoEntity.setChannel(ChannelEnum.dingding);
        outerOaAppInfoEntity.setOutEa(connectorVo.getDingCorpId());
        outerOaAppInfoEntity.setAppId(connectorVo.getAppKey());
        outerOaAppInfoEntity.setAppType(OuterOaAppInfoTypeEnum.selfBuild);
        outerOaAppInfoEntity.setStatus(OuterOaAppInfoStatusEnum.normal);

        DingtalkAppInfoParams appInfo = new DingtalkAppInfoParams();
        appInfo.setAppSecret(connectorVo.getAppSecret());
        appInfo.setRedirectAppId(connectorVo.getRedirectAppId());
        appInfo.setRedirectAppSecret(connectorVo.getRedirectAppSecret());
        appInfo.setClientIp(connectorVo.getClientIp());
        appInfo.setAgentId(connectorVo.getAgentId());
        appInfo.setNginxIp(connectorVo.getNginxIp());
        outerOaAppInfoEntity.setAppInfo(JSON.toJSONString(appInfo));
        return outerOaAppInfoEntity;
    }

    @Override
    public Result<String> doGetAuthUrl(String dataCenterId, ChannelEnum channel, OuterOaAppInfoTypeEnum outerOaAppInfoTypeEnum) {
        return Result.newError(ResultCodeEnum.SYS_ERROR);
    }

    @Override
    public List<AlertTypeEnum> getChannelSupportedAlertTypes() {
        // 返回钉钉自建应用支持的提醒类型
        // 根据AlertTypeEnum的实际定义使用正确的枚举值
        return Arrays.asList(
                AlertTypeEnum.CRM_TODO,      // CRM待办提醒
                AlertTypeEnum.CRM_NOTIFICATION   // CRM提醒
        );
    }

    @Override
    public Result<String> queryOuterEmpByValue(String dataCenterId, ChannelEnum channel, String queryFieldApiName, String queryFieldValue) {
        return Result.newError(ResultCodeEnum.SYS_ERROR);
    }

    @Override
    public Result<Void> refreshOuterEmpData(String dataCenterId, ChannelEnum channel) {
        // 检查是否已有任务在执行
        CompletableFuture<Result<Void>> existingTask = taskMap.get(dataCenterId);
        if (existingTask != null && !existingTask.isDone()) {
            log.info("Task is already running for dataCenterId: {}", dataCenterId);
            return Result.newError(ResultCodeEnum.PREVIOUS_TASK_IS_PROCESSED);
        }

        // 创建新的异步任务
        CompletableFuture<Result<Void>> future = CompletableFuture.supplyAsync(() -> {
            try {
                OuterOaEnterpriseBindEntity entity = outerOaEnterpriseBindManager.getEntityById(dataCenterId);
                final OuterOaAppInfoEntity appEntity = outerOaAppInfoManager.getEntity(ChannelEnum.dingding, entity.getOutEa(), entity.getAppId());
                final DingtalkAppInfoParams appInfo = JSON.parseObject(appEntity.getAppInfo(), DingtalkAppInfoParams.class);

                List<OutDeptData> detailList = refreshOuterDeptData(entity, appInfo);
                final Set<String> deptIds = detailList.stream().map(OutDeptData::getDeptId).map(String::valueOf).collect(Collectors.toSet());
                log.info("trace allPullEmployee deptIds:{} deptSize:{}", deptIds, detailList.size());
                outerOaDeptDataManager.deleteInvisibleDepts(entity.getOutEa(), ChannelEnum.dingding, entity.getAppId(), deptIds);

                Set<String> empIds = new HashSet<>();
                for (int i = 0; i < detailList.size(); i++) {
                    OutDeptData dept = detailList.get(i);
                    final Result<Void> voidResult = insertMappingEmp(dept, entity, appInfo, empIds);
                    if (!voidResult.isSuccess()) {
                        return Result.newError(voidResult.getCode(), voidResult.getMsg());
                    }
                }
                if (CollectionUtils.isNotEmpty(empIds)) {
                    outerOaEmployeeDataManager.deleteInvisibleUsers(entity.getOutEa(), ChannelEnum.dingding, entity.getAppId(), empIds);
                }
                return Result.newSuccess();
            } catch (Exception e) {
                log.error("Error processing async task for dataCenterId: {}", dataCenterId, e);
                return Result.newError(ResultCodeEnum.SYS_ERROR.getCode(), "Async processing error");
            } finally {
                taskMap.remove(dataCenterId);
            }
        });

        // 存储任务
        taskMap.put(dataCenterId, future);

        // 等待5秒看是否完成
        try {
            return future.get(5, TimeUnit.SECONDS);
        } catch (TimeoutException e) {
            log.info("Task is still processing for dataCenterId: {}", dataCenterId);
            return Result.newError(ResultCodeEnum.TASK_IS_PROCESSING);
        } catch (Exception e) {
            log.error("Error waiting for task completion for dataCenterId: {}", dataCenterId, e);
            return Result.newError(ResultCodeEnum.SYS_ERROR);
        }
    }

    @Override
    public Result<Boolean> routeNewPage(String tenantId, ChannelEnum channel) {
        return Result.newSuccess(true);
    }

    @Override
    public Result<Boolean> unbindConnect(String dataCenterId, ChannelEnum channel) {
        return Result.newError(ResultCodeEnum.SYS_ERROR);
    }

    @Override
    public Result<Void> employeeBindChangeEvent(String dataCenterId, List<String> ids, EmplyeeBindChangeTypeEnum type) {
        return null;
    }

    @Override
    public Result<UserTicketModel> getTicketByAppView(String ticket) {
        String value = redisDataSource.getRedisClient().get(GlobalValue.USER_TICKET_KEY_PREFIX + ticket);
        log.info("cgiLogin.getFsUser,ticket={},value={}", ticket, value);
        if (StringUtils.isEmpty(value)) {
            return Result.newError(ResultCodeEnum.TICKET_NOT_EXISTS);
        }
        UserTicketModel crmUserModel = JSONObject.parseObject(value, UserTicketModel.class);
        return Result.newSuccess(crmUserModel);

    }

    private List<OutDeptData> refreshOuterDeptData(OuterOaEnterpriseBindEntity entity, DingtalkAppInfoParams appInfo) {
        List<OutDeptData> detailList = Lists.newArrayList();
        //查询部门列表
        String appId = entity.getAppId();
        final DingConnectorVo connector = JSON.parseObject(entity.getConnectInfo(), DingConnectorVo.class);
        String appSecret = connector.getAppSecret();
        final int ei = eieaConverter.enterpriseAccountToId(entity.getFsEa());
        ScopeVo scopeVo = DingRequestUtil.queryScoreDeptEmployee(ei, connector.getClientIp(),
                appId, appSecret, appInfo.getToken(), tokenManager.getToken(ei, appId), connector.getNginxIp());
        log.info("requestDingAllDept,scopeVo={}", scopeVo);
        if (scopeVo == null) {
            log.warn("query dept list failed, ei={}, deptResponse={}.", ei, scopeVo);
            return detailList;
        }
        if (CollectionUtils.isEmpty(scopeVo.getAuthedDept())) {
            log.warn("query dept list failed, ei={}, deptResponse={}.", ei, scopeVo);
            return detailList;
        }
        //解析出部门，查询部门信息
        if (CollectionUtils.isNotEmpty(scopeVo.getAuthedDept())) {

            //如果scopeVo的部门为1.说明权限是全部门需要遍历全部部门
            List<OutDeptData> depts = new ArrayList<>();
            if (scopeVo.getAuthedDept().get(0) == 1) {
                final List<Dept> detailList1 = DingRequestUtil.queryDeptList(ei, connector.getClientIp(),
                        appId, appSecret, appInfo.getToken(), "1", connector.getNginxIp());
                detailList = convert2OutDeptData(detailList1);
                if (CollectionUtils.isEmpty(detailList)) {
                    log.warn("query dept list failed, ei={}, deptResponse={}.", ei, detailList);
                    return detailList;
                }
                final Dept dept = DingRequestUtil.queryDeptDetail(ei, connector.getClientIp(), tokenManager.getToken(ei, appId), appInfo.getToken(), 1L, connector.getNginxIp());
                OutDeptData deptDetail = convert2OutDeptData(dept);
                detailList.add(deptDetail);
            } else {
                for (int i = 0; i < scopeVo.getAuthedDept().size(); i++) {
                    List<Long> deptIdList = new ArrayList<>();
                    deptIdList.add(scopeVo.getAuthedDept().get(i));
                    //先把父级部门查询出来
                    List<OutDeptData> deptItem = convert2OutDeptData(DingRequestUtil.queryDeptList(ei, connector.getClientIp(),
                            appId, appSecret, appInfo.getToken(), scopeVo.getAuthedDept().get(i).toString(), connector.getNginxIp()));
                    //有可能没有子部门的情况
                    if (CollectionUtils.isNotEmpty(deptItem)) {
                        List<Long> collect = deptItem.stream().map(OutDeptData::getDeptId)
                                .collect(Collectors.toList());
                        deptIdList.addAll(collect);
                        depts.addAll(deptItem);
                    }
                    for (int j = 0; j < deptIdList.size(); j++) {
                        OutDeptData deptDetail = convert2OutDeptData(DingRequestUtil.queryDeptDetail(ei, connector.getClientIp(), tokenManager.getToken(ei, appId), appInfo.getToken(), deptIdList.get(j), connector.getNginxIp()));
                        detailList.add(deptDetail);
                    }
                }
                List<Long> scopeCollect = detailList.stream().map(OutDeptData::getDeptId)
                        .collect(Collectors.toList());
                scopeVo.setAuthedDept(scopeCollect);
            }
        }
        detailList = detailList.stream().filter(Objects::nonNull).collect(Collectors.toList());
        log.info("requestDingAllDept,detailList={}", detailList);

        outerOaDeptDataManager.batchUpdateOutDeptId(convert2OuterOaDeptDataEntity(detailList, entity.getOutEa(), entity.getAppId()));
        return detailList;
    }

    private List<OuterOaDeptDataEntity> convert2OuterOaDeptDataEntity(List<OutDeptData> detailList, String outEa, String appId) {
        return detailList.stream().map(outDeptData -> convert2OuterOaDeptDataEntity(outDeptData, outEa, appId)).collect(Collectors.toList());
    }

    private OuterOaDeptDataEntity convert2OuterOaDeptDataEntity(OutDeptData outDeptData, String outEa, String appId) {
        if (outDeptData == null) {
            return null;
        }

        OuterOaDeptDataEntity entity = new OuterOaDeptDataEntity();
        entity.setOutEa(outEa);
        entity.setChannel(ChannelEnum.dingding);
        entity.setAppId(appId);
        entity.setOutDeptId(String.valueOf(outDeptData.getDeptId()));
        entity.setDeptName(outDeptData.getName());
        entity.setParentDeptId(outDeptData.getParentId() != null ? String.valueOf(outDeptData.getParentId()) : null);
        entity.setDeptOrder(outDeptData.getSeq() != null ? Long.valueOf(outDeptData.getSeq()) : 0L);
        entity.setOutDeptInfo(JSON.parseObject(JSON.toJSONString(outDeptData)));
        entity.setCreateTime(System.currentTimeMillis());
        entity.setUpdateTime(System.currentTimeMillis());

        return entity;
    }

    private OutDeptData convert2OutDeptData(Dept dept) {
        if (dept == null) {
            return null;
        }

        OutDeptData outDeptData = new OutDeptData();
        outDeptData.setDeptId(dept.getId());
        outDeptData.setParentId(dept.getParentid());
        outDeptData.setName(dept.getName());
        outDeptData.setOwner(dept.getDeptOwner());

        return outDeptData;
    }

    private List<OutDeptData> convert2OutDeptData(List<Dept> deptList) {
        if (CollectionUtils.isEmpty(deptList)) {
            return new ArrayList<>();
        }
        return deptList.stream().map(this::convert2OutDeptData).collect(Collectors.toList());
    }

    private Result<Void> insertMappingEmp(OutDeptData dept, OuterOaEnterpriseBindEntity entity, DingtalkAppInfoParams appInfo, Set<String> empIds) {
        final String clientIp = appInfo.getClientIp();
        final String token = appInfo.getToken();
        final String appId = entity.getAppId();
        final String fsEa = entity.getFsEa();
        final int ei = eieaConverter.enterpriseAccountToId(fsEa);

        // 获取部门下的员工列表
        List<User> userListResult = DingRequestUtil.queryDeptUser(ei, clientIp, dept.getDeptId(), appId, appInfo.getAppSecret(), token, tokenManager.getToken(ei, appId), Long.valueOf(appInfo.getAgentId()), appInfo.getNginxIp());
        if (CollectionUtils.isEmpty(userListResult)) {
            log.warn("insertMappingEmp queryDeptUser failed or empty, deptId:{}", dept.getDeptId());
            return Result.newSuccess();
        }

        // 初始化员工映射信息
        Integer count = dingMappingEmployeeManager.initMappingEmployee(
                userListResult,
                ei,
                dept.getDeptId(),
                dept.getName(),
                appId
        );

        empIds.addAll(userListResult.stream().map(User::getUserid).collect(Collectors.toList()));

        log.info("insertMappingEmp success, deptId:{}, count:{}", dept.getDeptId(), count);
        return Result.newSuccess();
    }
}
