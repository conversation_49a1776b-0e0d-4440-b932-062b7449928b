package com.facishare.open.qywx.web.arg;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 规则参数实体类
 */
@Data
public class GetHitMsgListArg implements Serializable {
    /**
     * 上一次调用时返回的next_cursor
     */
    private String cursor;
    /**
     * 回调事件返回的 token 字段
     */
    private String token;
    /**
     * 是否需要消息详情，默认为否
     */
    private Integer need_detail;
    /**
     * 每次调用返回的记录数，默认为200，最大不超过1000
     */
    private Integer limit;

}