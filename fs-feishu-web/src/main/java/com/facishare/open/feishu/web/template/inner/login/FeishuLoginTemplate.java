package com.facishare.open.feishu.web.template.inner.login;

import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.erpdss.outer.oa.connector.base.inner.login.LoginTemplate;
import com.facishare.open.feishu.syncapi.model.login.FsUserModel;
import com.facishare.open.feishu.syncapi.model.login.LoginAuthModel;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.data.Code2UserInfoData;
import com.facishare.open.feishu.syncapi.service.LoginService;
import com.facishare.open.feishu.web.template.model.GenFsTicketModel;
import com.facishare.open.outer.oa.connector.common.api.login.UserTicketModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class FeishuLoginTemplate extends LoginTemplate {
    @Resource
    private LoginService loginService;

    @Override
    public void getOutUserInfoByCode(MethodContext context) {
        log.info("FeishuLoginTemplate.getOutUserInfoByCode, context={}", context);
        LoginAuthModel loginAuthModel = context.getData();

        Result<Code2UserInfoData> result = loginService.code2UserInfo(loginAuthModel);
        log.info("FeishuLoginTemplate.getOutUserInfoByCode, result={}", result);
        context.setResult(TemplateResult.newSuccess(result));
    }

    @Override
    public void genFsTicket(MethodContext context) {
        log.info("FeishuLoginTemplate.genFsTicket, context={}", context);
        UserTicketModel ticketModel = context.getData();
        Result<String> result = loginService.genFsTicket(ticketModel);
        log.info("FeishuLoginTemplate.genFsTicket, result={}", result);
        context.setResult(TemplateResult.newSuccess(result));
    }

    @Override
    public void getFsUserInfoByTicket(MethodContext context) {
        log.info("FeishuLoginTemplate.getFsUserInfoByTicket, context={}", context);
        String ticket = context.getData().toString();
        Result<FsUserModel> result = loginService.getFsUser(ticket);
        log.info("FeishuLoginTemplate.getFsUserInfoByTicket, result={}", result);
        context.setResult(TemplateResult.newSuccess(result));
    }
}
