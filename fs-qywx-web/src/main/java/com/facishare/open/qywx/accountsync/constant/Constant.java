package com.facishare.open.qywx.accountsync.constant;

/**
 * <AUTHOR>
 * @Date 2021/3/19 16:42
 * @Version 1.0
 */
// IgnoreI18nFile
public interface Constant {
    /**
     * 调文件系统传的业务组标识
     */
    String FS_CONSULT_BUSINESS = "FS_OPEN_QYWX";
    String UNSUPPORTED_MSG_TYPE = "【收到不支持的消息类型，暂无法显示】";
    String IMAGE_TYPE = "image";
    String DOC_TYPE = "doc";
    /**
     * 企业微信下单许可
     */
    String PAY_LICENSE_SUCCESS = "license_pay_success";
    // 自动激活
    String AUTO_ACTIVE = "auto_activate";

    String WechatInterfaceLicenseObj = "WechatInterfaceLicenseObj";

    String WechatEmployeeObj = "WechatEmployeeObj";

    /**
     * 企业微信数据智能提供的能力
     */
    String GET_CALL_BACK_DATA="get_callback_data";

    /**
     * 获取会话
     */
    String SYNC_MESSAGE="invoke_sync_msg";

    /**
     * 搜索会话
     */
    String SYNC_SEARCH_MESSAGE="invoke_search_msg";

    /**
     * 产生会话事件
     */
    String CONVERSATION_MESSAGE_TYPE="conversation_new_message";
    /**
     * 命中关键词规则通知
     */
    String HIT_KEYWORD="hit_keyword";

    /**
     * 企业微信callurl
     */
    String QYWX_CALL_DATA_URL="https://qyapi.weixin.qq.com/cgi-bin/chatdata/sync_call_program?access_token=%s";

    //企业绑定扩展字段
    String enterprise_extend = "{\"isFirstLand\":true,\"isRetainInformation\":false}";


}
