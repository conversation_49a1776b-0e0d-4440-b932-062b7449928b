package com.facishare.open.ding.web.utils;

import org.apache.commons.lang3.StringUtils;

/**
 * XSS防护工具类
 * 用于防止跨站脚本攻击(XSS)
 * 
 * @Author: AI Assistant
 * @Date: 2025-06-23
 * @Version: 1.0
 */
public class XssUtils {
    
    /**
     * HTML转义字符映射
     */
    private static final String[][] HTML_ESCAPE_CHARS = {
        {"&", "&amp;"},
        {"<", "&lt;"},
        {">", "&gt;"},
        {"\"", "&quot;"},
        {"'", "&#x27;"},
        {"/", "&#x2F;"},
        {"\\", "&#x5C;"},
        {"`", "&#x60;"},
        {"=", "&#x3D;"}
    };
    
    /**
     * 对字符串进行HTML转义，防止XSS攻击
     * 
     * @param input 需要转义的字符串
     * @return 转义后的安全字符串，如果输入为null则返回空字符串
     */
    public static String escapeHtml(String input) {
        if (StringUtils.isEmpty(input)) {
            return "";
        }
        
        String result = input;
        for (String[] escapeChar : HTML_ESCAPE_CHARS) {
            result = result.replace(escapeChar[0], escapeChar[1]);
        }
        
        return result;
    }
    
    /**
     * 对字符串进行JavaScript转义，用于在JavaScript代码中安全输出
     * 
     * @param input 需要转义的字符串
     * @return 转义后的安全字符串，如果输入为null则返回空字符串
     */
    public static String escapeJavaScript(String input) {
        if (StringUtils.isEmpty(input)) {
            return "";
        }
        
        return input.replace("\\", "\\\\")
                   .replace("\"", "\\\"")
                   .replace("'", "\\'")
                   .replace("\n", "\\n")
                   .replace("\r", "\\r")
                   .replace("\t", "\\t")
                   .replace("<", "\\u003C")
                   .replace(">", "\\u003E")
                   .replace("&", "\\u0026")
                   .replace("/", "\\/");
    }
    
    /**
     * 对用于JavaScript字符串的参数进行安全转义
     * 这个方法专门用于JSP中输出到JavaScript变量的场景
     * 
     * @param input 需要转义的字符串
     * @return 转义后的安全字符串，包含双引号包围
     */
    public static String escapeForJavaScriptString(String input) {
        if (input == null) {
            return "\"\"";
        }
        
        return "\"" + escapeJavaScript(input) + "\"";
    }
    
    /**
     * 检查字符串是否包含潜在的XSS攻击代码
     * 
     * @param input 需要检查的字符串
     * @return 如果包含潜在攻击代码返回true，否则返回false
     */
    public static boolean containsXssPattern(String input) {
        if (StringUtils.isEmpty(input)) {
            return false;
        }
        
        String lowerInput = input.toLowerCase();
        String[] xssPatterns = {
            "<script", "</script>", "javascript:", "onload=", "onerror=", 
            "onclick=", "onmouseover=", "onfocus=", "onblur=", "onchange=",
            "onsubmit=", "onreset=", "onselect=", "onkeydown=", "onkeypress=",
            "onkeyup=", "alert(", "confirm(", "prompt(", "eval(", "expression("
        };
        
        for (String pattern : xssPatterns) {
            if (lowerInput.contains(pattern)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 清理字符串中的潜在XSS攻击代码
     * 
     * @param input 需要清理的字符串
     * @return 清理后的安全字符串
     */
    public static String cleanXss(String input) {
        if (StringUtils.isEmpty(input)) {
            return "";
        }
        
        // 移除script标签
        String result = input.replaceAll("(?i)<script[^>]*>.*?</script>", "");
        
        // 移除javascript:协议
        result = result.replaceAll("(?i)javascript:", "");
        
        // 移除事件处理器
        result = result.replaceAll("(?i)on\\w+\\s*=", "");
        
        // 移除style中的expression
        result = result.replaceAll("(?i)expression\\s*\\(", "");
        
        return result;
    }
}
