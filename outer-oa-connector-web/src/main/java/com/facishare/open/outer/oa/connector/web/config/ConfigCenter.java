package com.facishare.open.outer.oa.connector.web.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 配置中心
 */
@Slf4j
@Component
public class ConfigCenter {
    /**
     * 接收告警企业
     */
    public static String NOTIFICATION_EA = "fszdbd2575";
    /**
     * erp数据同步appId，这里共用一下
     */
    public static String ERP_SYNC_DATA_APP_ID = "FSAID_9897f5";
    /**
     * 消息体最长数量
     */
    public static Integer NOTICE_MAX_SIZE = 500;
    /**
     * 文件预览路径，%s为带ext的npath
     */
    public static String PREVIEW_FILE_PATH = "https://www.ceshi112.com/FSC/EM/File/GetByPath?path=%s";
    /**
     * 接收告警人
     */
    public static Set<String> NOTIFICATION_MEMBERS = Sets.newHashSet();

    /**
     * 配置中心管理员Map
     * key: 企业EA
     * value: 管理员ID列表
     */
    private static  Map<String, List<Integer>> configCenterAdminMap = Maps.newHashMap();

    static {
        ConfigFactory.getInstance().getConfig("fs-feishu-config", config -> {
            NOTIFICATION_EA = config.get("NOTIFICATION_EA", NOTIFICATION_EA);
            ERP_SYNC_DATA_APP_ID = config.get("ERP_SYNC_DATA_APP_ID", ERP_SYNC_DATA_APP_ID);
            PREVIEW_FILE_PATH = config.get("PREVIEW_FILE_PATH", PREVIEW_FILE_PATH);
            configCenterAdminMap=JSONObject.parseObject(config.get("oa_admins_user","{}"),new TypeReference<Map<String, List<Integer>>>() {});
        });


    }

    /**
     * 判断是否是配置中心管理员
     *
     * @param ea         企业EA
     * @param employeeId 员工ID
     * @return true:是配置中心管理员 false:不是配置中心管理员
     */
    public static boolean isConfigCenterAdmin(String ea, Integer employeeId) {
        if (StringUtils.isEmpty(ea) || employeeId == null) {
            return false;
        }
        List<Integer> adminList = configCenterAdminMap.get(ea);
        if (adminList == null) {
            return false;
        }
        return adminList.contains(employeeId);
    }


    /**
     * 移除配置中心管理员
     *
     * @param ea         企业EA
     * @param employeeId 员工ID
     */
    public static void removeConfigCenterAdmin(String ea, Integer employeeId) {
        if (StringUtils.isEmpty(ea) || employeeId == null) {
            return;
        }
        List<Integer> adminList = configCenterAdminMap.get(ea);
        if (adminList != null) {
            adminList.remove(employeeId);
            log.info("ConfigCenter.removeConfigCenterAdmin success, ea={}, employeeId={}, adminMap={}", 
                    ea, employeeId, JSON.toJSONString(configCenterAdminMap));
        }
    }
}
