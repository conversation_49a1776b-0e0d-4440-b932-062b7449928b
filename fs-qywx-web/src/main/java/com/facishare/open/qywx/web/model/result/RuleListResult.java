package com.facishare.open.qywx.web.model.result;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class RuleListResult implements Serializable {
    private List<RuleList> rule_list;
    private Boolean has_more;
    private String next_cursor;

    @Data
    private class RuleList implements Serializable{
        /**
         * 规则id
         */
        private String rule_id;
        /**
         * 规则名称
         */
        private String name;
        /**
         * 创建时间
         */
        private Long create_time;

    }
}
