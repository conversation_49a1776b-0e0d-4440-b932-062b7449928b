package com.facishare.open.ding.provider.service;

import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.ding.template.login.LoginService;
import com.facishare.open.ding.template.model.Code2UserInfoData;
import com.facishare.open.ding.template.model.FsUserModel;
import com.facishare.open.ding.template.model.LoginAuthModel;
import com.facishare.open.ding.web.redis.RedisDataSource;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEmployeeBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEmployeeBindManager;
import com.facishare.open.outer.oa.connector.common.api.constants.GlobalValue;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.login.UserTicketModel;
import com.facishare.open.outer.oa.connector.common.api.result.Result;
import com.facishare.open.outer.oa.connector.common.api.result.ResultCodeEnum;
import com.facishare.webhook.common.util.MD5Util;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class DingLoginServiceImpl implements LoginService {
    @Autowired
    private RedisDataSource redisDataSource;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private OuterOaEmployeeBindManager outerOaEmployeeBindManager;

    @Override
    public Result<Code2UserInfoData> code2UserInfo(LoginAuthModel loginAuthModel) {
        return null;
    }

    @Override
    public Result<String> genFsTicket(UserTicketModel userTicketModel) {
        String ticketFormat = "appId=%s&corpId=%s&outUserId=%s&fsEa=%s&timestamp=%s";
        long timestamp = System.currentTimeMillis();
        String ticketMd5 = UserTicketModel.convertMd5String(userTicketModel);
        String key = GlobalValue.USER_TICKET_KEY_PREFIX + ticketMd5;
        redisDataSource.getRedisClient().set(key, JSONObject.toJSONString(userTicketModel));
        redisDataSource.getRedisClient().expire(key, GlobalValue.USER_TICKET_EXPIRE_TIME);  //10分钟有效
        log.info("DingLoginServiceImpl.genFsTicket,ticketMd5={}",ticketMd5);
        return new Result<>(ticketMd5);
    }

    @Override
    public Result<FsUserModel> getFsUser(String ticket) {
        String key = GlobalValue.USER_TICKET_KEY_PREFIX + ticket;
        String value = redisDataSource.getRedisClient().get(key);
        log.info("DingLoginServiceImpl.getFsUser,key={},value={}",key,value);
        if(StringUtils.isEmpty(value)) {
            return Result.newError(ResultCodeEnum.TICKET_NOT_EXISTS);
        }
        UserTicketModel ticketModel = JSONObject.parseObject(value,UserTicketModel.class);
        log.info("DingLoginServiceImpl.getFsUser,ticketModel={}",ticketModel);
        long offset = System.currentTimeMillis() - ticketModel.getTimestamp();
        log.info("DingLoginServiceImpl.getFsUser,offset={}",offset);
        if(offset > GlobalValue.USER_TICKET_EXPIRE_TIME * 1000L) {
            log.info("DingLoginServiceImpl.getFsUser,ticket expired");
            return Result.newError(ResultCodeEnum.TICKET_EXPIRED);
        }
        final OuterOaEmployeeBindEntity employeeMapping = outerOaEmployeeBindManager.queryNormalByFsEaAndOutEmpId(ChannelEnum.dingding, ticketModel.getFsEa(), ticketModel.getAppId(), ticketModel.getOuterUserId());

        log.info("DingLoginServiceImpl.getFsUser,employeeMapping={}",employeeMapping);
        if(ObjectUtils.isEmpty(employeeMapping)) {
            return Result.newError(ResultCodeEnum.EMPLOYEE_NO_BIND_INFO);
        }
        FsUserModel fsUserModel = new FsUserModel(ticketModel.getFsEa(), employeeMapping.getFsEmpId(),ticketModel.getAppId());
        log.info("DingLoginServiceImpl.getFsUser,fsUserModel={}",fsUserModel);
        return Result.newSuccess(fsUserModel);
    }
}
