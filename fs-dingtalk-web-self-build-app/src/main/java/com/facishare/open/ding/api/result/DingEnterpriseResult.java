package com.facishare.open.ding.api.result;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * <p>企业绑定Result</p>
 *
 * @<NAME_EMAIL>
 * @version 1.0
 * @dateTime 2018/7/12 11:04
 */
@Data
public class DingEnterpriseResult implements Serializable {

    private static final long serialVersionUID = -22275816140032131L;

    private Long id;

    /**
     * 纷享ea
     **/
    private String ea;

    /**
     * 纷享EI
     **/
    private Integer ei;

    /**
     * 纷享企业名称
     **/
    private String enterpriseName;

    /**
     * 钉钉企业标识
     **/
    private String dingCorpId;

    private String agentId;

    /**
     * 应用ID
     **/
    private String appKey;

    /**
     * 应用secret
     **/
    private String appSecret;

    /**
     * 免等待应用ID
     **/
    private String redirectAppId;

    /**
     * 免等待应用secret
     **/
    private String redirectAppSecret;

    private String clientIp;

    private String nginxIp;

    private String token;

    /**
     * 创建时间
     **/
    private Date createTime;

    /**
     * 更新时间
     **/
    private Date updateTime;

    /**
     * 创建人
     **/
    private Integer createBy;

    /**
     * 修改人
     **/
    private Integer updateBy;

    private Integer isBind;

    private Integer isCallback;

    private Integer devModel;

    //crm待办和提醒开关状态 0：关闭全部 1：推送待办 2：推送提醒 3：推送待办和提醒
    private Integer alertStatus;

    //重名后缀全局index
    private Integer allIndex;

    //账号自动绑定
    private Integer autBind;
    /**
     * 待办推送的位置  0：工作通知 1：钉钉待办 2：都推送
     */
    private Integer todoType;


}
