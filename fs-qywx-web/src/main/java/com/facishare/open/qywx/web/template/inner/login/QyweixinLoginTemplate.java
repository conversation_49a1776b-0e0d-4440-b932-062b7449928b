package com.facishare.open.qywx.web.template.inner.login;

import com.alibaba.fastjson.JSONObject;
import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.erpdss.outer.oa.connector.base.inner.login.LoginTemplate;
import com.facishare.open.order.contacts.proxy.api.datasource.RedisDataSource;
import com.facishare.open.outer.oa.connector.common.api.constants.GlobalValue;
import com.facishare.open.outer.oa.connector.common.api.login.UserTicketModel;
import com.facishare.open.qywx.accountinner.service.QyweixinGatewayInnerService;
import com.facishare.open.qywx.web.template.model.GenFsTicketModel;
import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinRepUserDetailInfoRsp;
import com.facishare.open.qywx.accountsync.result.ErrorRefer;
import com.facishare.open.qywx.accountsync.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class QyweixinLoginTemplate extends LoginTemplate {
    @Resource
    private QyweixinGatewayInnerService qyweixinGatewayInnerService;
    @Autowired
    private RedisDataSource redisDataSource;

    @Override
    public void getOutUserInfoByCode(MethodContext context) {
        log.info("QyweixinLoginTemplate.getOutUserInfoByCode, context={}", context);
        Map<String, Object> data = context.getData();
        String code = (String) data.get("code");
        String appId = (String) data.get("appId");
        String outEa = (String) data.get("outEa");
        boolean isIsvCode = (Boolean) data.get("isIsvCode");

        if(isIsvCode) {
            Result<Object> result = qyweixinGatewayInnerService.code2AppLoginUserInfo(code, appId, outEa);
            log.info("QyweixinLoginTemplate.getOutUserInfoByCode, result={}", result);
            context.setResult(TemplateResult.newSuccess(result));
        } else {
            Result<QyweixinRepUserDetailInfoRsp> repAppLoginUserInfo = qyweixinGatewayInnerService.getRepAppLoginUserInfo(appId, outEa, code);
            log.info("QyweixinLoginTemplate.getOutUserInfoByCode, repAppLoginUserInfo={}", repAppLoginUserInfo);
            context.setResult(TemplateResult.newSuccess(repAppLoginUserInfo));
        }
    }

    @Override
    public void genFsTicket(MethodContext context) {
        log.info("QyweixinLoginTemplate.genFsTicket, context={}", context);
        UserTicketModel ticketModel = context.getData();
        Result<String> result = qyweixinGatewayInnerService.genFsTicket(ticketModel);
        log.info("QyweixinLoginTemplate.genFsTicket, result={}", result);
        context.setResult(TemplateResult.newSuccess(result));
    }

    @Override
    public void getFsUserInfoByTicket(MethodContext context) {
        log.info("QyweixinLoginTemplate.getFsUserInfoByTicket, context={}", context);
        Map paramMap = context.getData();
        String key = GlobalValue.USER_TICKET_KEY_PREFIX + paramMap.get("ticket");
        String userTicketInfo = redisDataSource.getRedisClient().get(key);
        log.info("QyweixinLoginTemplate.getQyweixinCurrentUser, paramMap={}, userTicketInfo={}", paramMap, userTicketInfo);

        //检查ticket是否有效
        if(StringUtils.isBlank(userTicketInfo)){
            log.info("从redis中找不到对应的ticket信息,ticket={}",  paramMap.get("ticket"));
            context.setResult(TemplateResult.newErrorData(ErrorRefer.INVALID_TICKET));
            return;
        }
        UserTicketModel userInfo = JSONObject.parseObject(userTicketInfo, UserTicketModel.class);


        Map<String, Object> userInfoMap = new HashMap<>();
        userInfoMap.put("userTicketInfo", userTicketInfo);
        userInfoMap.put("userInfo", userInfo);
        log.info("QyweixinLoginTemplate.getFsUserInfoByTicket, userInfoMap={}", userInfoMap);
        context.setResult(TemplateResult.newSuccess(userInfoMap));
    }
}
