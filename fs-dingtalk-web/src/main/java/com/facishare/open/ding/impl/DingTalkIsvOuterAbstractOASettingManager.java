package com.facishare.open.ding.impl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.open.ding.cloud.manager.DingManager;
import com.facishare.open.ding.common.model.Dept;
import com.facishare.open.ding.common.model.EmployeeDingVo;
import com.facishare.open.ding.common.model.User;
import com.facishare.open.ding.provider.redis.RedisDataSource;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaAppInfoEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaDeptDataEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.dingtalk.OutDeptData;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaAppInfoManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaDeptDataManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEmployeeDataManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.outer.oa.connector.common.api.admin.DingConnectorVo;
import com.facishare.open.outer.oa.connector.common.api.constants.GlobalValue;
import com.facishare.open.outer.oa.connector.common.api.enums.AlertTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.EmplyeeBindChangeTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaAppInfoTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.login.UserTicketModel;
import com.facishare.open.outer.oa.connector.common.api.object.DingTalkEmployeeObject;
import com.facishare.open.outer.oa.connector.common.api.outerInterface.OuterAbstractSettingService;
import com.facishare.open.outer.oa.connector.common.api.params.DingtalkAppInfoParams;
import com.facishare.open.outer.oa.connector.common.api.result.OuterOAConnectSettingResult;
import com.facishare.open.outer.oa.connector.common.api.result.Result;
import com.facishare.open.outer.oa.connector.common.api.result.ResultCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

/**
 * 钉钉自建应用的外部OA设置管理器
 *
 * <AUTHOR>
 * @date 2025/3/27 20:45:16
 */
@Component("dingTalkIsvManager")
@Slf4j
public class DingTalkIsvOuterAbstractOASettingManager implements OuterAbstractSettingService<DingConnectorVo> {

    private final ConcurrentHashMap<String, CompletableFuture<Result<Void>>> taskMap = new ConcurrentHashMap<>();

    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;

    @Autowired
    private OuterOaEmployeeDataManager outerOaEmployeeDataManager;

    @Autowired
    private OuterOaAppInfoManager outerOaAppInfoManager;

    @Autowired
    private OuterOaDeptDataManager outerOaDeptDataManager;

    @Autowired
    private DingManager dingManager;
    @Autowired
    private RedisDataSource redisDataSource;

    @Override
    public Result<Void> doValidateConfigAndSave(OuterOAConnectSettingResult outerOAConnectSettingResult, ChannelEnum channel, OuterOaAppInfoTypeEnum outerOaAppInfoTypeEnum) {
        // 钉钉isv不支持
        return Result.newError(ResultCodeEnum.SYS_ERROR);
    }

    @Override
    public Result<String> doGetAuthUrl(String dataCenterId, ChannelEnum channel, OuterOaAppInfoTypeEnum outerOaAppInfoTypeEnum) {
        return Result.newError(ResultCodeEnum.SYS_ERROR);
    }

    @Override
    public List<AlertTypeEnum> getChannelSupportedAlertTypes() {
        // 返回钉钉自建应用支持的提醒类型
        // 根据AlertTypeEnum的实际定义使用正确的枚举值
        return Arrays.asList(
                AlertTypeEnum.CRM_TODO,      // CRM待办提醒
                AlertTypeEnum.CRM_NOTIFICATION   // CRM提醒
        );
    }

    @Override
    public Result<String> queryOuterEmpByValue(String dataCenterId, ChannelEnum channel, String queryFieldApiName, String queryFieldValue) {
        return Result.newError(ResultCodeEnum.SYS_ERROR);
    }

    @Override
    public Result<Void> refreshOuterEmpData(String dataCenterId, ChannelEnum channel) {
        // 检查是否已有任务在执行
        CompletableFuture<Result<Void>> existingTask = taskMap.get(dataCenterId);
        if (existingTask != null && !existingTask.isDone()) {
            log.info("Task is already running for dataCenterId: {}", dataCenterId);
            return Result.newError(ResultCodeEnum.PREVIOUS_TASK_IS_PROCESSED);
        }

        // 创建新的异步任务
        CompletableFuture<Result<Void>> future = CompletableFuture.supplyAsync(() -> {
            try {
                OuterOaEnterpriseBindEntity entity = outerOaEnterpriseBindManager.getEntityById(dataCenterId);
                final OuterOaAppInfoEntity appEntity = outerOaAppInfoManager.getEntity(ChannelEnum.dingding, entity.getOutEa(), entity.getAppId());
                final DingtalkAppInfoParams appInfo = JSON.parseObject(appEntity.getAppInfo(), DingtalkAppInfoParams.class);

                List<OutDeptData> detailList = refreshOuterDeptData(entity, appInfo);
                final Set<String> deptIds = detailList.stream().map(OutDeptData::getDeptId).map(String::valueOf).collect(Collectors.toSet());
                log.info("trace allPullEmployee deptIds:{} deptSize:{}", deptIds, detailList.size());
                outerOaDeptDataManager.deleteInvisibleDepts(entity.getOutEa(), ChannelEnum.dingding, entity.getAppId(), deptIds);

                Set<String> empIds = new HashSet<>();
                for (int i = 0; i < detailList.size(); i++) {
                    OutDeptData dept = detailList.get(i);
                    final Result<Void> voidResult = insertMappingEmp(dept, entity, appInfo, empIds);
                    if (!voidResult.isSuccess()) {
                        return Result.newError(voidResult.getCode(), voidResult.getMsg());
                    }
                }
                if (CollectionUtils.isNotEmpty(empIds)) {
                    outerOaEmployeeDataManager.deleteInvisibleUsers(entity.getOutEa(), ChannelEnum.dingding, entity.getAppId(), empIds);
                }
                return Result.newSuccess();
            } catch (Exception e) {
                log.error("Error processing async task for dataCenterId: {}", dataCenterId, e);
                return Result.newError(ResultCodeEnum.SYS_ERROR.getCode(), "Async processing error");
            } finally {
                taskMap.remove(dataCenterId);
            }
        });

        // 存储任务
        taskMap.put(dataCenterId, future);

        // 等待5秒看是否完成
        try {
            return future.get(5, TimeUnit.SECONDS);
        } catch (TimeoutException e) {
            log.info("Task is still processing for dataCenterId: {}", dataCenterId);
            return Result.newError(ResultCodeEnum.TASK_IS_PROCESSING);
        } catch (Exception e) {
            log.error("Error waiting for task completion for dataCenterId: {}", dataCenterId, e);
            return Result.newError(ResultCodeEnum.SYS_ERROR);
        }
    }

    private List<OutDeptData> refreshOuterDeptData(OuterOaEnterpriseBindEntity entity, DingtalkAppInfoParams appInfo) {
        List<OutDeptData> detailList;
        //查询部门列表
        final List<Dept> allVisibleDepts = dingManager.getAllVisibleDepts(entity.getOutEa(), String.valueOf(appInfo.getSuiteId()));

        detailList = convert2OutDeptData(allVisibleDepts).stream().filter(Objects::nonNull).collect(Collectors.toList());
        log.info("requestDingAllDept,detailList={}", detailList);

        outerOaDeptDataManager.batchUpdateOutDeptId(convert2OuterOaDeptDataEntity(detailList, entity.getOutEa(), entity.getAppId()));
        return detailList;
    }

    private List<OuterOaDeptDataEntity> convert2OuterOaDeptDataEntity(List<OutDeptData> detailList, String outEa, String appId) {
        return detailList.stream().map(outDeptData -> convert2OuterOaDeptDataEntity(outDeptData, outEa, appId)).collect(Collectors.toList());
    }

    private OuterOaDeptDataEntity convert2OuterOaDeptDataEntity(OutDeptData outDeptData, String outEa, String appId) {
        if (outDeptData == null) {
            return null;
        }

        OuterOaDeptDataEntity entity = new OuterOaDeptDataEntity();
        entity.setOutEa(outEa);
        entity.setChannel(ChannelEnum.dingding);
        entity.setAppId(appId);
        entity.setOutDeptId(String.valueOf(outDeptData.getDeptId()));
        entity.setDeptName(outDeptData.getName());
        entity.setParentDeptId(outDeptData.getParentId() != null ? String.valueOf(outDeptData.getParentId()) : null);
        entity.setDeptOrder(outDeptData.getSeq() != null ? Long.valueOf(outDeptData.getSeq()) : 0L);
        entity.setOutDeptInfo(JSON.parseObject(JSON.toJSONString(outDeptData)));
        entity.setCreateTime(System.currentTimeMillis());
        entity.setUpdateTime(System.currentTimeMillis());

        return entity;
    }

    private OutDeptData convert2OutDeptData(Dept dept) {
        if (dept == null) {
            return null;
        }

        OutDeptData outDeptData = new OutDeptData();
        outDeptData.setDeptId(dept.getId());
        outDeptData.setParentId(dept.getParentid());
        outDeptData.setName(dept.getName());
        outDeptData.setOwner(dept.getDeptOwner());

        return outDeptData;
    }

    private List<OutDeptData> convert2OutDeptData(List<Dept> deptList) {
        if (CollectionUtils.isEmpty(deptList)) {
            return new ArrayList<>();
        }
        return deptList.stream().map(this::convert2OutDeptData).collect(Collectors.toList());
    }

    private Result<Void> insertMappingEmp(OutDeptData dept, OuterOaEnterpriseBindEntity entity, DingtalkAppInfoParams appInfo, Set<String> empIds) {
        final String appId = entity.getAppId();
        final String fsEa = entity.getFsEa();

        // 获取部门下的员工列表
        List<EmployeeDingVo> userListResult = dingManager.queryUserByDept(entity.getOutEa(), dept.getDeptId(), String.valueOf(appInfo.getSuiteId()));
        if (CollectionUtils.isEmpty(userListResult)) {
            log.warn("insertMappingEmp queryDeptUser failed or empty, deptId:{}", dept.getDeptId());
            return Result.newSuccess();
        }

        // 创建一个列表来收集所有要更新的员工数据实体
        List<DingTalkEmployeeObject> dataEntities = new ArrayList<>();

        final String dcId = outerOaEnterpriseBindManager.getDcIdByEaAndAppId(ChannelEnum.dingding, fsEa, appId);

        for (EmployeeDingVo user : userListResult) {
            // 创建员工数据
            DingTalkEmployeeObject employeeData = new DingTalkEmployeeObject();
            employeeData.setUserid(user.getUserid());
            employeeData.setName(user.getName());
            employeeData.setPhone(user.getMobile());
            employeeData.setDeptId(dept.getDeptId());
            employeeData.setDeptName(dept.getName());
            employeeData.setEmail(user.getEmail());
            employeeData.setUnionId(user.getUnionid());

            // 添加到集合而不是直接插入
            dataEntities.add(employeeData);
        }

        // 批量更新或插入员工数据
        final Integer count = outerOaEmployeeDataManager.batchUpsert(dataEntities, ChannelEnum.dingding, dcId);

        empIds.addAll(userListResult.stream().map(EmployeeDingVo::getUserid).collect(Collectors.toList()));

        // 初始化员工映射信息
        log.info("insertMappingEmp success, deptId:{}, count:{}", dept.getDeptId(), count);
        return Result.newSuccess();
    }

    @Override
    public Result<Boolean> routeNewPage(String tenantId, ChannelEnum channel) {
        // 钉钉isv直接全网
        return Result.newSuccess(true);
    }

    @Override
    public Result<Boolean> unbindConnect(String dataCenterId, ChannelEnum channel) {
        return Result.newError(ResultCodeEnum.SYS_ERROR);
    }

    @Override
    public Result<Void> employeeBindChangeEvent(String dataCenterId, List<String> ids, EmplyeeBindChangeTypeEnum type) {
        return null;
    }

    @Override
    public Result<UserTicketModel> getTicketByAppView(String ticket) {
        String value = redisDataSource.getRedisClient().get(GlobalValue.USER_TICKET_KEY_PREFIX + ticket);
        LogUtils.info("cgiLogin.getFsUser,ticket={},value={}", ticket, value);
        if (StringUtils.isEmpty(value)) {
            return Result.newError(ResultCodeEnum.TICKET_NOT_EXISTS);
        }
        UserTicketModel crmUserModel = JSONObject.parseObject(value, UserTicketModel.class);
        return Result.newSuccess(crmUserModel);

    }
}
