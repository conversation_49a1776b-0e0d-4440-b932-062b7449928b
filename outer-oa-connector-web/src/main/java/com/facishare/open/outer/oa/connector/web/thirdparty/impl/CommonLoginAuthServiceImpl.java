package com.facishare.open.outer.oa.connector.web.thirdparty.impl;

import com.alibaba.fastjson.JSONObject;
import com.facishare.open.oa.base.dbproxy.ch.manager.OAConnectorOpenDataManager;
import com.facishare.open.oa.base.dbproxy.ch.manager.OAConnectorOpenDataModel;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEmployeeBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEmployeeBindManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEmployeeBindParams;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEnterpriseBindParams;
import com.facishare.open.order.contacts.proxy.api.datasource.RedisDataSource;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.outer.oa.connector.common.api.constants.GlobalValue;
import com.facishare.open.outer.oa.connector.common.api.enums.BindTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.OADataTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaAppInfoTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.login.CrmUserModel;
import com.facishare.open.outer.oa.connector.common.api.login.UserTicketModel;
import com.facishare.open.outer.oa.connector.common.api.outerInterface.CommonLoginAuthService;
import com.facishare.open.outer.oa.connector.common.api.outerInterface.OuterAbstractSettingService;
import com.facishare.open.outer.oa.connector.common.api.params.SendTextNoticeArg;
import com.facishare.open.outer.oa.connector.common.api.result.Result;
import com.facishare.open.outer.oa.connector.common.api.result.ResultCodeEnum;
import com.facishare.open.outer.oa.connector.web.config.ConfigCenter;
import com.facishare.open.outer.oa.connector.web.manager.OuterOASettingFactory;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class CommonLoginAuthServiceImpl  implements CommonLoginAuthService {

    @Autowired
    private RedisDataSource redisDataSource;
    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Autowired
    private OuterOaEmployeeBindManager outerOaEmployeeBindManager;
    @Autowired
    private OAConnectorOpenDataManager oaConnectorOpenDataManager;
    @Resource
    private NotificationManager notificationManager;
    @Autowired
    private OuterOASettingFactory outerOASettingFactory;


    @Override
    public Result<UserTicketModel> getCrmUserByTicket(String ticket,String channel) {
        String key = GlobalValue.USER_TICKET_KEY_PREFIX + ticket;
        ChannelEnum channelEnum=ChannelEnum.getChannelByAppView(channel);
        OuterAbstractSettingService implementation = outerOASettingFactory
                .getImplementation(channelEnum, OuterOaAppInfoTypeEnum.isv);
        Result<UserTicketModel> ticketByAppView = implementation.getTicketByAppView(ticket);
        if(!ticketByAppView.isSuccess()||ticketByAppView.getData()==null){
            implementation= outerOASettingFactory.getImplementation(channelEnum, OuterOaAppInfoTypeEnum.selfBuild);
            ticketByAppView = implementation.getTicketByAppView(ticket);
        }
        if(!ticketByAppView.isSuccess()||ticketByAppView.getData()==null){
            return Result.newError(ResultCodeEnum.TICKET_NOT_EXISTS);
        }
        LogUtils.info("cgiLogin.getFsUser,key={},value={}", key, ticketByAppView);

        UserTicketModel crmUserModel = ticketByAppView.getData();
        long offset = System.currentTimeMillis() - crmUserModel.getTimestamp();
        LogUtils.info("FeishuServiceImpl.getFsUser,offset={}", offset);
        if (offset > GlobalValue.USER_TICKET_EXPIRE_TIME * 1000L) {
            LogUtils.info("FeishuServiceImpl.getFsUser,ticket expired");
            return Result.newError(ResultCodeEnum.TICKET_EXPIRED);
        }
        OuterOaEmployeeBindParams outerOaEmployeeBindParams = OuterOaEmployeeBindParams.builder()
                .fsEa(crmUserModel.getFsEa())
                .outEa(crmUserModel.getCorpId())
                .outEmpId(crmUserModel.getOuterUserId())
                .build();
        List<OuterOaEmployeeBindEntity> entities = outerOaEmployeeBindManager.getEntities(outerOaEmployeeBindParams);
        LogUtils.info("FeishuServiceImpl.getFsUser,entity={}", entities);
        if (ObjectUtils.isEmpty(entities)) {
            List<OuterOaEnterpriseBindEntity> enterpriseBindList = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().outEa(crmUserModel.getCorpId()).build());
            OuterOaEnterpriseBindEntity enterpriseBindEntity = enterpriseBindList.get(0);
            if (enterpriseBindEntity.getBindType().equals(BindTypeEnum.auto)) {
                //上报
                OAConnectorOpenDataModel model = OAConnectorOpenDataModel.builder()
                        .ea(crmUserModel.getFsEa())
                        .channelId(enterpriseBindEntity.getChannel().name())
                        .dataTypeId(OADataTypeEnum.EMPLOYEE_LOGIN.getDataType())
                        .corpId(crmUserModel.getCorpId())
                        .outUserId(crmUserModel.getOuterUserId())
                        .errorCode("103")
                        .errorMsg("ticket换取纷享员工身份失败，请及时关注！") //ignorei18n
                        .build();
                oaConnectorOpenDataManager.send(model);
                //告警
                SendTextNoticeArg arg = new SendTextNoticeArg();
                arg.setEnterpriseAccount(ConfigCenter.NOTIFICATION_EA);
                List<String> receivers = new LinkedList<>(ConfigCenter.NOTIFICATION_MEMBERS);
                arg.setReceivers(receivers.stream().map(Integer::valueOf).collect(Collectors.toList()));
                arg.setMsgTitle("登陆纷享失败告警:" + enterpriseBindEntity.getChannel().name()); //ignorei18n
                String msg = String.format("ticket换取纷享员工身份失败\nticket=%s，info=%s\n请及时关注！", ticket, crmUserModel); //ignorei18n
                arg.setMsg(msg);
                notificationManager.sendNotice(arg);
            }
            return Result.newError(ResultCodeEnum.EMPLOYEE_NO_BIND_INFO);
        }
        OuterOaEmployeeBindEntity entity=entities.get(0);
        LogUtils.info("FeishuServiceImpl.getFsUser,fsUserModel={}",crmUserModel);
        return Result.newSuccess(crmUserModel);
    }

}
