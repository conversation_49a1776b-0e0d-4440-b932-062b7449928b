package com.facishare.open.feishu.web.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.erpdss.outer.oa.connector.base.outer.event.order.OpenEnterpriseHandlerTemplate;
import com.facishare.open.feishu.sync.manager.FeishuOuterOaConnectManager;
import com.facishare.open.feishu.sync.manager.ToolsManager;
import com.facishare.open.feishu.sync.mapper.EmployeeBindMapper;
import com.facishare.open.feishu.syncapi.config.ConfigCenter;
import com.facishare.open.feishu.syncapi.consts.OutUrlConsts;
import com.facishare.open.feishu.syncapi.entity.EmployeeBindEntity;
import com.facishare.open.feishu.syncapi.enums.ApprovalStatusEnum;
import com.facishare.open.feishu.syncapi.model.event.FeishuOrderPaidEvent;
import com.facishare.open.feishu.syncapi.model.event2.FeishuContactUserCreatedV3Event;
import com.facishare.open.feishu.syncapi.model.externalApprovals.ExternalApprovalsTaskUpdateDetail;
import com.facishare.open.feishu.syncapi.model.externalApprovals.ExternalInstancesDetail;
import com.facishare.open.feishu.syncapi.model.jsapi.JsApiSignatureModel;
import com.facishare.open.feishu.syncapi.model.login.FsUserModel;
import com.facishare.open.feishu.syncapi.model.login.LoginAuthModel;
import com.facishare.open.feishu.syncapi.result.ExternalApprovalTaskResult;
import com.facishare.open.feishu.syncapi.result.ExternalInstancesDetailResult;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.ResultCodeEnum;
import com.facishare.open.feishu.syncapi.result.data.Code2UserInfoData;
import com.facishare.open.feishu.syncapi.result.data.UserData;
import com.facishare.open.feishu.syncapi.service.ContactsService;
import com.facishare.open.feishu.syncapi.service.ExternalApprovalsService;
import com.facishare.open.feishu.syncapi.service.LoginService;
import com.facishare.open.feishu.syncapi.vo.InstanceVo.ExternalMessageInstanceVo;
import com.facishare.open.feishu.web.BaseTest;
import com.facishare.open.feishu.web.controller.outer.feishu.FeishuEventController;
import com.facishare.open.feishu.web.controller.outer.feishu.FeishuExternalController;
import com.facishare.open.feishu.web.controller.outer.feishu.FeishuInternalController;
import com.facishare.open.oa.base.dbproxy.configVo.ConstantDb;
import com.facishare.open.oa.base.dbproxy.manager.ObjectDataManager;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaAppInfoEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEmployeeBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaMessageBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.*;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaMessageBindParams;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.outer.oa.connector.common.api.enums.*;
import com.facishare.open.outer.oa.connector.common.api.result.OuterOAConnectSettingResult;
import com.facishare.open.outer.oa.connector.i18n.I18NStringManager;
import com.fxiaoke.crmrestapi.arg.ActionAddArg;
import com.fxiaoke.crmrestapi.arg.ControllerListArg;
import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.Page;
import com.fxiaoke.crmrestapi.common.data.SearchQuery;
import com.fxiaoke.crmrestapi.result.ActionAddResult;
import com.fxiaoke.crmrestapi.service.MetadataActionService;
import com.fxiaoke.crmrestapi.service.MetadataControllerService;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.zookeeper.Login;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.List;

public class FeishuEventControllerTest extends BaseTest {
    @Resource
    private FeishuEventController feishuEventController;
    @Resource
    private FeishuInternalController feishuInternalController;
    @Resource
    private FeishuExternalController feishuExternalController;
    @Resource
    private LoginService loginService;

    @Resource
    private I18NStringManager i18NStringManager;
    @Autowired
    private FeishuOuterOaConnectManager feishuOuterOaConnectManager;
    @Autowired
    private ObjectDataManager objectDataManager;
    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Autowired
    private ToolsManager toolsManager;
    @Autowired
    private EmployeeBindMapper employeeBindMapper;
    @Autowired
    private ContactsService contactsService;
    @Autowired
    private OuterOaAppInfoManager outerOaAppInfoManager;
    @Autowired
    private OpenEnterpriseHandlerTemplate openEnterpriseHandlerTemplate;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private OuterOaMessageBindManager outerOaMessageBindManager;
    @Autowired
    private ExternalApprovalsService externalApprovalsService;
    @Autowired
    private MetadataControllerService metadataControllerService;
    @Autowired
    private MetadataActionService metadataActionService;
    @Autowired
    private OuterOaConfigInfoManager outerOaConfigInfoManager;

    @Test
    public void push() {
        String json = "{\"encrypt\":\"f3geJabFtna7oDOYb73Lhm2ALJ6S7sZIiJcQNK9WvSFhIErQhtqVzPXlpRTaBxrndV5rHFFhPnVMa5ieJoBuajkxvZetCe0gNDSyuxVO0mFIMC4XzH+utkIHqzTmp5hrmdIp/MlRbQ1V5PYl0H5BSYuWrGIF3qIrwBEotkpB+Beog8EHbuySd+v6z8yPI2wL1uiPMRAf+RyS4yF6C6sxniPtEDzPXuqU6fmKXnWEvI6XjAkbkqtgKZybz0enkXJxG8xMgaP5y/Ue2NaG1UAu6sU7OHrc8GQwOlGpsEIAeGLo4hgizX4R1kYmuQ9+iaLP8yx/BaCuNJ0m6l+oc8I9XLURlqcjvYQmR4DZ9LmcNdWyrbeZObt+6x6eFbOlk4sllQbnSafrS/hF1zzpTGyxYM+OavVAyNtZz8wyHq6ySoXzfx1x12IEXiopxa067PBTwHZSKfe1AwMk+Law6BPY+H2A5OEQ8YQn+E6tIGBVeHibu23yPlO337CwgnyK6uuixnN86AJ9gwhzifhRnDtyxDSzHfPhsIZH/+4FJZafXdjyjPDsOVHnVrtTLUZRgEuxdUZQ+6kse8n8EYq4xpA7jrKYQzjJx5fyfXY9npmg4pQw5gnaI5dNhlsPHA53iMk06zA0og7ZWbqfRQVH8V3R67dSZjQ+PveV4TWtPA9r9OEyHd2nGoKQib7mkBinqpDWuuz9VAMQCfMXkwCpnNY4SHj0ZLDQnGFqlMMrSYkFAT5p5IujyPE4Rv/+R8r9Um03Wbv+nzUpc69EdH9C1048+r+a4M8swGf8Q1WxVmSoPrgn5SkMfc7njRKu7xAklpEjLD4locevBUx+9xJ2um6rQcJBaCtYbJ/VcUCTsrF3uT1sY55gLm6WYO2HADMwTrfFialZSvG/FuY8A4bhvzZHQ2dnmmvYcYhCTw75cDK2kthhQPYVQe3D7V0cu/NduLdGhIWcr2CF/eDpsBuaU8D6pB/km/7AIXYox238GakS8jvIrBffn9/DRusvdJuyzXrlZ1KI8fd2giqYzbEcLak0agwFk3TJYVkG++RcIm+EHcvMYfkxhgO+OG52zMyIhYAa6eVCqL3nK6V95UJwPpD56LuhIkwImGoEblGxBVI9hitmmVt/7f2/u/9r/yb4z7zu8DELRk+AgjhIPHTWXEdmqTD2SwcWK2ujq+LhALw0PMFdbqRHxPcdpOAmBEF19jZ7evZizvWeIUfkrgibuiuh+6Yx2blbrrHDA0CzDIbZHy5/xpPN14kw9wpLi051VLLw7gAyN8S3LrMx/cxXuIyBh3n0emKYJ3hqsmVXzZwsw54irkysc9Q7qGkqFT0ttr8ZB47N5RDp3S/MUoNkNlBvZ/xruce7gnBEOS7UKSXIP4bMADkh/Xo1vj2Uk9VR7UIRJ42kjk+7+KnjIHhSaCqkFXeLal6UjR7c9tLoJK8Qrdjf9MRi8TKCdUri5u/O3o1Kj4P7uWPANAyw2Gk+63cQBjgRtaS5WxBeq/ePhjO5/2tKoDtNJaWVtcYHjgIqcRNr5Y8Vsa9LrxvrXgzoSmi3CTSXeqkM4Kt/Ze+TMo/kRlMvspsDfLZclfKBoJShL/+LgmHohyh7Tk3v8RZmLnfjyPvBqm4SxEAeVMo54a86m4Nv0ZRGaiEgv336OSZwiXGCsLNmz2iMHCmryLajQe5MRUJY14HarBEPoia/cE0Zf6eVtdPM/+56xSOq7GDHsy1hSCIFqAVDlHKeUiEk33vsLYyHJ3+0G0KhCkyCjLn1IMqeW5iNexsL+anV6IYM4ph0yere1q8DXwryN0Fo7xJy1bwa8OBy4dG7Lki6UBztjNAkJ1SXq4/0Is5+JxqEhnMHyr9cgtBawC5xW7pobPZ9Ky0+mDaXO7vR7KqRHD0+H4rOyWLidqtprs6XJ693NX0rrpiNECgivcF0UVeUXEcAuRej7L3XDf+5k532dm3kvMAydbZMr9jVOMoWPbQN/6x2\"}";
        String json1 = "{\"encrypt\":\"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\"}";
        String json2 = "{\"encrypt\":\"a9sRQxF9e9fWitoRryYFsAlW+G4uGI30XNb1dE/fbzFTbhSTetKN6RWHbKpYlvZ7DdpUOjPuhF6PGXazgVz794U6MMU8Wbilh3CkzeS1VgeGSSmd0l5vp7bgw2ueFIAw62eoo8k9gA3SbJwwih7RuXMd+LAvdEHi6ezx6hrMFFippqdzgLpLGxbIMEDOEOTOWN23XXuTUrff+n0utO6a/w==\"}";
        String push = feishuEventController.push(json2);
        System.out.println(push);
    }

    @Test
    public void getJsApiSignature() {
        Result<JsApiSignatureModel> push = feishuInternalController.getJsApiSignature("cli_a20192f6afb8d00c",
                "https://crm.ceshi112.com/hcrm/index", null, null);
        System.out.println(push);
    }

    @Test
    public void code2UserInfo() {
        Result<Code2UserInfoData> result = loginService
                .code2UserInfo(LoginAuthModel.builder().code("87a9dc03be1d1cc690dd18bf90397bcd").build());
        System.out.println(result);
    }

    @Test
    public void getFsUser() {
        Result<FsUserModel> result = loginService.getFsUser("06ebd75c1fa88e82804116c0b6f67b75");
        System.out.println(result);
    }

    @Test
    public void i18NStringManagerGet() {

        String result = i18NStringManager.get("erpdss_outer_oa_connector.feishu.global.s4", "en", "88521", "");
        System.out.println(result);
        String defaultLang = i18NStringManager.getDefaultLang("88521");
        System.out.println(defaultLang);
    }

    @Test
    public void testData() {
        String data = "{\n" + "    \"connectParams\": {\n" + "            \"feishu\": {\n"
                + "                \"dataCenterId\": \"67e6862d20c3d0b0e330910d\",\n"
                + "                \"connectorName\": \"飞书\",\n" + "                \"channel\": \"feishu\",\n"
                + "                \"alertConfig\": false,\n" + "                \"enterpriseName\":\"飞书测试\",\n"
                + "                \"appId\":\"cli_a75815139bbc500e\",\n"
                + "                \"appSecret\":\"LZMQ9BHfCczQ4jeXlAsjkcXvzNAixLhg\"\n" + "               \n"
                + "            }\n" + "        },\n" + "        \"channelEnum\": \"feishu\",\n"
                + "        \"outerOaAppInfoTypeEnum\":\"selfBuild\",\n"
                + "        \"currentDcId\":\"67e6862d20c3d0b0e330910d\"\n" + "}";
        feishuOuterOaConnectManager.refreshOuterEmpData("67e6862d20c3d0b0e330910d", ChannelEnum.feishu);

    }

    @Test
    public void testData2() {
        Integer tenantId = 91449;
        String data="{\n" +
                "  \"vice_departments\" : [ \"999999\" ],\n" +
                "  \"leader\" : [\"1322\"],\n" +
                "  \"phone\" : \"18070809022\",\n" +
                "  \"object_describe_api_name\" : \"PersonnelObj\",\n" +
                "  \"name\" : \"测试汇报部门xx\",\n" +
                "  \"employee_number\" : \"\",\n" +
                "  \"field_m7q2c__c\" : \"\",\n" +
                "  \"main_department\" : [ \"1011\" ],\n" +
                "  \"email\" : \"\",\n" +
                "  \"status\" : \"0\"\n" +
                "}";
        ObjectData objectData=JSONObject.parseObject(data,ObjectData.class);
        objectData.remove("vice_departments");
        objectData.remove("main_department");
        objectData.remove("phone");
        HeaderObj headerObj = HeaderObj.newInstance(tenantId, ConstantDb.CRM_OPERATOR_USER_ID);
        ActionAddArg actionAddArg = new ActionAddArg();
        actionAddArg.setObjectData(objectData);
        com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> actionAddResultResult = metadataActionService
                .add(headerObj, AccountTypeEnum.EMP_BIND.getCode(), false, false, false, false, actionAddArg);
        LogUtils.info("action data:{}",actionAddResultResult);

//        OuterOaEnterpriseBindEntity entityById = outerOaEnterpriseBindManager.getEntityById("67f5ce157e1ac00001a4ae91");
//        com.facishare.open.outer.oa.connector.common.api.result.Result<String> stringResult = feishuOuterOaConnectManager
//                .doGetAuthUrl(entityById.getConnectInfo(), ChannelEnum.feishu, OuterOaAppInfoTypeEnum.selfBuild);
//
//        com.facishare.open.outer.oa.connector.common.api.result.Result<ActionAddResult> employee = objectDataManager
//                .createEmployee(entityById, "ou_df96cbec34d677b6b259b9d3780f916a");

    }

    @Test
    public void testTools() {
        String data = "{\n" +
                "  \"data\": {\n" +
                "    \"orderId\": \"7495089689885048836\",\n" +
                "    \"pricePlanId\": \"price_a7e8c5803b56500d\",\n" +
                "    \"pricePlanType\": \"trial\",\n" +
                "    \"seats\": 0,\n" +
                "    \"buyCount\": 1,\n" +
                "    \"createTime\": 1745089572000,\n" +
                "    \"payTime\": 1745089572000,\n" +
                "    \"buyType\": \"buy\",\n" +
                "    \"srcOrderId\": \"\",\n" +
                "    \"orderPayPrice\": 0\n" +
                "  },\n" +
                "  \"result\": {\n" +
                "    \"code\": 0,\n" +
                "    \"msg\": null,\n" +
                "    \"data\": null\n" +
                "  }\n" +
                "}";
        MethodContext methodContext = JSONObject.parseObject(data, MethodContext.class);
        // 创建分页对象，使用MyBatis-Plus的标准分页方式
//        Page<EmployeeBindEntity> page = new Page<>(1, 1);
//
//        // 构建查询条件
//        LambdaQueryWrapper<EmployeeBindEntity> wrapper = new LambdaQueryWrapper<>();
//        wrapper.eq(EmployeeBindEntity::getFsEa, "82777");
//
//        // 执行分页查询
//        Page<EmployeeBindEntity> employeeBindEntityPage = employeeBindMapper.selectPage(page, wrapper);
//        String jsonString = JSONObject.toJSONString(employeeBindEntityPage);
//        System.out.println(jsonString);

        toolsManager.transfer("88521");
    }

    @Test
    public void refreshData() {
        // 重新获取全部数据
        feishuOuterOaConnectManager.refreshOuterEmpData("67f5ce157e1ac00001a4ae90", ChannelEnum.feishu);
    }

    @Test
    public void testRemoveData() {
        ChannelEnum channelEnum = ChannelEnum.getByCode(ConfigCenter.CURRENT_CHANNEL_ISV_CHANNEL);
        objectDataManager.removeEmpData(outerOaEnterpriseBindManager.getEntityById("67eb6084e4ca8900012d37de"), "ou_aabee732b1f92ac92b4bf124922e98a9", RemoveEmployeeEventType.RESIGN_EMPLOYEE);
    }

    @Test
    public void testRemove() {

        String eventData = "{\n" +
                "    \"object\": {\n" +
                "        \"country\": \"\",\n" +
                "        \"work_station\": \"\",\n" +
                "        \"gender\": 0,\n" +
                "        \"city\": \"\",\n" +
                "        \"open_id\": \"ou_ff83f8e19f43c690a943f19045bcd3be\",\n" +
                "        \"mobile\": \"+8618909808800\",\n" +
                "        \"employee_no\": \"\",\n" +
                "        \"avatar\": {\n" +
                "            \"avatar_640\": \"https://s1-imfile.feishucdn.com/static-resource/v1/v3_00lf_c1e71d93-2719-4c9f-9301-dfcd093808dg~?image_size=640x640&cut_type=&quality=&format=png&sticker_format=.webp\",\n" +
                "            \"avatar_origin\": \"https://s1-imfile.feishucdn.com/static-resource/v1/v3_00lf_c1e71d93-2719-4c9f-9301-dfcd093808dg~?image_size=noop&cut_type=&quality=&format=png&sticker_format=.webp\",\n" +
                "            \"avatar_72\": \"https://s1-imfile.feishucdn.com/static-resource/v1/v3_00lf_c1e71d93-2719-4c9f-9301-dfcd093808dg~?image_size=72x72&cut_type=&quality=&format=png&sticker_format=.webp\",\n" +
                "            \"avatar_240\": \"https://s1-imfile.feishucdn.com/static-resource/v1/v3_00lf_c1e71d93-2719-4c9f-9301-dfcd093808dg~?image_size=240x240&cut_type=&quality=&format=png&sticker_format=.webp\"\n" +
                "        },\n" +
                "        \"department_ids\": [\n" +
                "            \"od-993a12df001fa30db7461a7ce5651ca8\"\n" +
                "        ],\n" +
                "        \"enterprise_email\": \"\",\n" +
                "        \"join_time\": 1744934400,\n" +
                "        \"employee_type\": 1,\n" +
                "        \"name\": \"小南\",\n" +
                "        \"nickname\": \"\",\n" +
                "        \"union_id\": \"on_272b71006fa65ecb29d5aab107f82694\",\n" +
                "        \"en_name\": \"\",\n" +
                "        \"orders\": [\n" +
                "            {\n" +
                "                \"user_order\": 0,\n" +
                "                \"department_id\": \"od-993a12df001fa30db7461a7ce5651ca8\",\n" +
                "                \"is_primary_dept\": true,\n" +
                "                \"department_order\": 1\n" +
                "            }\n" +
                "        ],\n" +
                "        \"job_title\": \"\",\n" +
                "        \"status\": {\n" +
                "            \"is_activated\": false,\n" +
                "            \"is_frozen\": false,\n" +
                "            \"is_resigned\": false,\n" +
                "            \"is_unjoin\": true,\n" +
                "            \"is_exited\": false\n" +
                "        }\n" +
                "    }\n" +
                "}";
        FeishuContactUserCreatedV3Event event = JSON.parseObject(eventData, FeishuContactUserCreatedV3Event.class);


        String appId = "cli_a75815139bbc500e";
        String outEa = "142f6d8a511b975e";
        OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity = outerOaEnterpriseBindManager.getEntityById("67f5ce157e1ac00001a4ae91");
        String data = "{\n" +
                "    \"country\": \"\",\n" +
                "    \"work_station\": \"\",\n" +
                "    \"gender\": 0,\n" +
                "    \"city\": \"\",\n" +
                "    \"open_id\": \"ou_bea51554ab3aa5ba7f63009974cc2222\",\n" +
                "    \"mobile\": \"+8618708089097\",\n" +
                "    \"employee_no\": \"\",\n" +
                "    \"avatar\": {\n" +
                "        \"avatar_640\": \"https://s1-imfile.feishucdn.com/static-resource/v1/v3_00l4_0db31c83-60fd-4979-a1af-b044686d2bbg~?image_size=640x640&cut_type=&quality=&format=png&sticker_format=.webp\",\n" +
                "        \"avatar_origin\": \"https://s1-imfile.feishucdn.com/static-resource/v1/v3_00l4_0db31c83-60fd-4979-a1af-b044686d2bbg~?image_size=noop&cut_type=&quality=&format=png&sticker_format=.webp\",\n" +
                "        \"avatar_72\": \"https://s1-imfile.feishucdn.com/static-resource/v1/v3_00l4_0db31c83-60fd-4979-a1af-b044686d2bbg~?image_size=72x72&cut_type=&quality=&format=png&sticker_format=.webp\",\n" +
                "        \"avatar_240\": \"https://s1-imfile.feishucdn.com/static-resource/v1/v3_00l4_0db31c83-60fd-4979-a1af-b044686d2bbg~?image_size=240x240&cut_type=&quality=&format=png&sticker_format=.webp\"\n" +
                "    },\n" +
                "    \"enterprise_email\": \"\",\n" +
                "    \"employee_type\": 1,\n" +
                "    \"name\": \"测试飞书\",\n" +
                "    \"nickname\": \"\",\n" +
                "    \"union_id\": \"on_5b041f2e4a4678d3c96c2e14abbc96c5\",\n" +
                "    \"en_name\": \"\",\n" +
                "    \"job_title\": \"\",\n" +
                "    \"status\": {\n" +
                "        \"is_activated\": false,\n" +
                "        \"is_frozen\": false,\n" +
                "        \"is_resigned\": true,\n" +
                "        \"is_unjoin\": true,\n" +
                "        \"is_exited\": false\n" +
                "    }\n" +
                "}";
        UserData.User user = JSONObject.parseObject(data, UserData.User.class);
        Result<Void> removedUserList = contactsService.removeUserList(appId, outEa, Lists.newArrayList(user), outerOaEnterpriseBindEntity);
    }

    @Test
    public void testDataFix() {
        String ea = "91449";
        Long startTime = 1745391188970l;
        Long endTime = 1745391188975l;
        LogUtils.info("fixTodo，tenantId：{}，start_time：{}，end_time：{}", ea, startTime, endTime);
        Integer tenantId = eieaConverter.enterpriseAccountToId(ea);
        OuterOaEnterpriseBindEntity enterpriseBindEntity = outerOaEnterpriseBindManager.getEntity(ChannelEnum.feishu, ea, null, null);

        try {
            // 创建查询参数
            OuterOaMessageBindParams params = OuterOaMessageBindParams.builder()
                    .channel(ChannelEnum.feishu)
                    .fsEa(ea)
                    .build();

            // 使用自定义方法查询带时间范围的消息数据
            List<OuterOaMessageBindEntity> messages = outerOaMessageBindManager.getEntitiesByTimeRange(params, startTime, endTime);
            for (OuterOaMessageBindEntity message : messages) {
                //需要拿到待办的消息
                try {
                    List<ObjectData> objectData = queryApprovalTaskData(tenantId, message.getSourceId());
                    if (ObjectUtils.isNotEmpty(objectData)) {
                        ObjectData itemData = objectData.get(0);
                        String state = String.valueOf(itemData.get("state"));
                        if (true) {
                            /**
                             * (data=DealTodoPushArg(msgType=commonMsg, upstreamEa=null,
                             * enterpriseBindEntity=OuterOaEnterpriseBindEntity(id=6803c9d8afceeb0001ab19b6, channel=feishu,
                             * fsEa=dcg000034, outEa=2e9d0b8ea98f5654, appId=cli_a20192f6afb8d00c, connectInfo={"alertConfig":true,
                             * "alertTypes":["CRM_TODO","CRM_NOTIFICATION"],"appId":"cli_a20192f6afb8d00c","appType":"isv",
                             * "channel":"feishu","connectorName":"飞书连接器","corpId":"2e9d0b8ea98f5654","dataCenterName":"神州数码",
                             * "displayId":"F245228343","enterpriseName":"神州数码","isFirstLand":true,"isRetainInformation":false},
                             * bindType=manual, bindStatus=normal, createTime=1745078744317, updateTime=1745142343858),
                             * dealTodoArg=DealTodoArg(super=BaseExternalArg(generateUrlType=0, extraDataMap={}, groupKeys=[feishu],
                             * appId=null), ea=dcg000034, ei=650993, operators=[5696],
                             * sourceId=6805e8ad1cc251641812d167, bizType=452, handleUserIds=[5696],
                             * taskStatusEnum=null, extDataMap={opinions=[{"reply_user":["5696"],
                             * "action_type":"agree","reply_time":1745228006328,"id":"680610e60696b117fa311c8a","opinion":""}], state=pass})),
                             * result=TemplateResult(code=0, msg=null, data=null))
                             */
                            //in_progress 审批中的数据
                            //需要将审批人改为已处理
                            if (message.getMessageType().equals(OuterOaMessageBindMsgTypeEnum.todo)) {
                                //需要根据detail，重新推送
                                ExternalMessageInstanceVo externalMessageInstanceVo = convertMessageInfo(message.getMessageInfo());
                                ExternalInstancesDetail externalInstancesDetail = externalMessageInstanceVo.getTodoDetail();
                                for (ExternalInstancesDetail.Task task : externalInstancesDetail.getTaskList()) {
                                    task.setStatus(ApprovalStatusEnum.APPROVED.name());
                                }
                                // 组装数据
                                String curTime = System.currentTimeMillis() + "";
                                externalInstancesDetail.setStatus(ApprovalStatusEnum.APPROVED.name());
                                Result<ExternalInstancesDetailResult> instancesDetailResultResult = externalApprovalsService.syncExternalApprovals(enterpriseBindEntity.getOutEa(), message.getAppId(), externalInstancesDetail);
                                LogUtils.info("external approval taskids:{}", instancesDetailResultResult);
                            } else {
                                if (message.getMessageType().equals(OuterOaMessageBindMsgTypeEnum.bot)) {
                                    //bot消息
                                    ExternalApprovalsTaskUpdateDetail externalApprovalsTaskUpdateDetail = new ExternalApprovalsTaskUpdateDetail();
                                    externalApprovalsTaskUpdateDetail.setStatus(ApprovalStatusEnum.APPROVED.name());
                                    externalApprovalsTaskUpdateDetail.setMessageId(message.getTaskId());
                                    Result<ExternalApprovalTaskResult> externalApprovalTaskResultResult = externalApprovalsService.updateExternalApprovalTask(message.getOutEa(), message.getAppId(), externalApprovalsTaskUpdateDetail);
                                    LogUtils.info("external approval taskids:{}", externalApprovalTaskResultResult);
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    LogUtils.warn("fix error approval", e);
                }
            }

        } catch (Exception e) {
            LogUtils.error("fixTodo执行异常", e);

        }
    }

    private ExternalMessageInstanceVo convertMessageInfo(String messageInfo) {
        ExternalMessageInstanceVo externalMessageInstanceVo = JSONObject.parseObject(messageInfo, new TypeReference<ExternalMessageInstanceVo>() {
        });
        //兼容之前的数据
        ExternalInstancesDetail externalInstancesDetail = null;
        if (ObjectUtils.isEmpty(externalMessageInstanceVo) || ObjectUtils.isEmpty(externalMessageInstanceVo.getTodoDetail())) {
            externalMessageInstanceVo = new ExternalMessageInstanceVo();
            externalInstancesDetail = JSONObject.parseObject(messageInfo, new TypeReference<ExternalInstancesDetail>() {
            });
            externalMessageInstanceVo.setTodoDetail(externalInstancesDetail);

        } else {
            externalInstancesDetail = externalMessageInstanceVo.getTodoDetail();
        }
        return externalMessageInstanceVo;
    }

    /**
     * 查询crm审批数据
     *
     * @param tenantId
     * @param dataId
     * @return
     */
    public List<ObjectData> queryApprovalTaskData(Integer tenantId, String dataId) {

        HeaderObj headerObj = HeaderObj.newInstance(tenantId, CrmConstants.SYSTEM_USER);
        String erpOrgObj = "ApprovalTaskObj";
        ControllerListArg listArg = new ControllerListArg();
        List<String> fieldValues = Lists.newArrayList();
        fieldValues.add(dataId);
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.setLimit(10);
        searchQuery.addFilter("_id", fieldValues, "In");
        listArg.setSearchQuery(searchQuery);
        com.fxiaoke.crmrestapi.common.result.Result<Page<ObjectData>> dataListRes = metadataControllerService.list(headerObj, erpOrgObj, listArg);
        LogUtils.info("ApprovalTaskManager data={}", dataListRes);
        if (!dataListRes.isSuccess()) {
            LogUtils.warn("list erp org obj failed,tenantId:{},res:{}", tenantId, dataListRes);
            return null;
        }
        return dataListRes.getData().getDataList();
    }

    @Test
    public void testDataLogin(){
        loginUrl("cli_a75815139bbc500e","91449");
    }
    private String loginUrl(String appId,String fsEa){
        JSONObject redirectUriJson = null;
        if( outerOaConfigInfoManager.getRouteErpDssLogin(fsEa)){
            redirectUriJson= JSONObject.parseObject(ConfigCenter.feishu_redirect_uri_gray);
        }
        String feishuAppidUrl = redirectUriJson.getString(appId);

        if(ObjectUtils.isNotEmpty(feishuAppidUrl)){
            return feishuAppidUrl;
        }
        return OutUrlConsts.feishu_crmurl;
    }
}

