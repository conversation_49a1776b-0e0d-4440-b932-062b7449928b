package com.facishare.open.qywx.old.accountsync.test;

import com.facishare.open.qywx.BaseTest;
import com.facishare.open.qywx.accountinner.model.QYWXConnectParam;
import com.facishare.open.qywx.accountinner.service.QyweixinGatewayInnerService;
import com.facishare.open.qywx.accountsync.model.EnterpriseWeChatEventProto;
import com.facishare.open.qywx.accountsync.model.qyweixin.*;
import com.facishare.open.qywx.accountsync.result.Result;
import com.facishare.open.qywx.web.service.impl.QyweixinGatewayInnerServiceImpl;
import com.facishare.open.qywx.web.threadpool.ThreadPoolHelper;
import com.facishare.open.oa.base.dbproxy.utils.SecurityUtil;
import com.facishare.open.qywx.accountsync.utils.xml.*;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import javax.annotation.Resource;
import java.io.BufferedWriter;
import java.io.FileWriter;
import java.io.IOException;
import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Created by liuwei on 2018/07/20
 */
@Slf4j
//@RunWith(SpringJUnit4ClassRunner.class)
//@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
public class QyweixinGatewayInnerTest extends BaseTest {

    @Resource
    private QyweixinGatewayInnerService qyweixinGatewayInnerService;


    @Test
    public void recvMsgEvent() {
        String msgSignature = "3f53583500ae536996e48fff2bcdc69c9602a250";
        String timeStamp = "**********";
        String nonce = "**********";
        String postData = "<xml><ToUserName><![CDATA[wx4c7edab730f4fdc9]]></ToUserName><Encrypt><![CDATA[mwQ/QQbtFzcMFKLyTI+oKzAzvHbZ4ZuquHFcCRdIl6UJyLCSMHKO4UwERAhmTMFRc67evUjcYVExSGogDtvKPh+yhQx9foIELI3eDqTGx83DwqRHFwA/4dAwdRnBZ9/EbCRTga3pP3/NfQKdeZoJX6vjKtUq7nD0YCxDM0znq96uHCKU/OCRMX4+8L1VItpU+Wjtye9yvpbMaErNFT3ToNG5VMkObI2D7JeD4RtHPU5rLv/NQNPgC59+mAGUzzhZda2zv1xX0I5dSPG3v7e61xCrhJWaOrzBqIJrSlYyTFD5H3OnofVZB3ehDwPHUSdDtmCEtXlXKqgpNAAxQVE/WZQjyuTarXwrn9/lsKqyhDsCB3w2/Cgiszz74+1pfPdc]]></Encrypt><AgentID><![CDATA[]]></AgentID></xml>";
        String echostr = "";  //接口验证， 会带有
//        String result = qyweixinGatewayInnerService.recvMsgEvent(msgSignature, timeStamp, nonce, postData, echostr, "wx4c7edab730f4fdc9");
//        log.info("trace recvMsgEvent result:{}", result);
//        Assert.assertEquals("success", result);
    }

    @Test
    public void recvMsgEventCheckUrl() {
        String msgSignature = "d0aa88888c7538672036470c010b0ea3844362b0";
        String timeStamp = "1532673751";
        String nonce = "1532457298";
        String postData = "";
        String echostr = "gHAPrjElkRrjuRc4CsIwk5ZeXyCEHB+AI+fa0tE8UVCTzn9sSa37NgGSeyP4kaftKFH44sIThrfXGbA5+E8WrA==";  //接口验证， 会带有
//        String result = qyweixinGatewayInnerService.recvMsgEvent(msgSignature, timeStamp, nonce, postData, echostr, "wx4c7edab730f4fdc9");
//        log.info("trace recvMsgEventCheckUrl result:{}", result);
    }

    @Test
    public void recvMsgEventUpdateUser() {
        String msgSignature = "f4bd03859d4fb43b919799994099737c17efd5a3";
        String timeStamp = "1533887744";
        String nonce = "1533414865";
        String postData = "<xml><ToUserName><![CDATA[wx4c7edab730f4fdc9]]></ToUserName><Encrypt><![CDATA[mTfjUcNBhI0ytfAev+mso+jADpFZGmS7DFjL8vLh4CQGrg8QRF+C7txc6Gzi83bQ+QWJ09X0Ldy9800egG3ZN9I4WxNBPcG7y1gV6tZhq9f3GH6Hjy+y3O5Kp2WkjCP/ZysHY4PmRqD0VQpwlnBPFA2D0aIAnjQyTAsfnZQbo66bQbc3WX1V9Otk2crz4O0eEWJQWcEo1iohqq6JSI9uC/U3kCuUme185kI+e0bE9N+M9Di0OBbW6khAAEd+tsp+BE6q6qCtfBk//GHGy6tnuzx6oXJ8qhQDyh+0CuqOA0e6DpKYCpa8rjH+sx15Nr/cdiKKhgQqjZ8wBPGUZz/Q4O36Lx0+VroCb39XdtXT5dvYHTtiDvE8+ft69S8nF61bbUpooc5hQUYJzQ6Rz7OTAPlwYzWjm/XxzXAomLQQVihidCGGzhJcYsLPjMeXIFr2SxDtIIDk0n549Pdp2MaykTlnl+CnNPSFWGvOSugZPV53dgUKUVboH1HDm6c4m0oRoaTd+hAevh+ze2BiioNQF3opqd14+E/d6F4HLp2Cv+E=]]></Encrypt><AgentID><![CDATA[]]></AgentID></xml>";
        String echostr = "";  //接口验证， 会带有
//        String result = qyweixinGatewayInnerService.recvMsgEvent(msgSignature, timeStamp, nonce, postData, echostr, appId);
//        log.info("trace recvMsgEventCheckUrl result:{}", result);
//        Assert.assertEquals("success", result);
    }

    @Test
    public void recvMsgEventUpdateAppAuth() {
        String msgSignature = "d6546baaa52f42fcb49139a3c10d6edb9a01fdf4";
        String timeStamp = "1533894479";
        String nonce = "1533924121";
        String postData = "<xml><ToUserName><![CDATA[wx4c7edab730f4fdc9]]></ToUserName><Encrypt><![CDATA[H0CG9oHHfOm0f+XJWBZyeH7GbbK7JQerxpCAvrMZ29tW0rNlrBlvyLpyxyjrYw8B7XUgkreMQFM5jvFXMj+0YeCby/8Y3T829/zgzR5i/ZB1AzWcunuAlf9AjOBicwiredC0ZtjiY8BiA8jwaj4nXsu6isfWHnY7nFyXEtiXy4i5GKDOur9QJalN5S+P8scdrD8uQY/cjduPVRvF2GYvyg2M1XfEBd/s8mimunzMtvCwEf8M6phahaYQktFxBxRkdWBCMSx8nX3bC3mMBvztFi/8cVN1cOTTf6bgS0HpyTdbiMl1rH2YLLrm5Zb8iYJkjapLpbYihztsyawAAM+Psw==]]></Encrypt><AgentID><![CDATA[]]></AgentID></xml>";
        String echostr = "";  //接口验证， 会带有
//        String result = qyweixinGatewayInnerService.recvMsgEvent(msgSignature, timeStamp, nonce, postData, echostr, appId);
//        log.info("trace recvMsgEventUpdateAppAuth result:{}", result);
//        Assert.assertEquals("success", result);
    }


    @Test
    public void recvMsgEventSaveSuiteToken() {
        String msgSignature = "00ca695e4c87b14b5978e3dc2d01c77e95e8dcd2";
        String timeStamp = "1535698277";
        String nonce = "1535413651";
        String postData = "<xml><ToUserName><![CDATA[wx4c7edab730f4" +
                "fdc9]]></ToUserName><Encrypt><![CDATA[gi9C578h6XgsFYq8VH9Tt518pH9UyDuKCKSyeSf3uoqbZ+Jkdrg160FW7O04z1rksI1bGXx2XVlTpSeD9f3R2LwxRWnySHNjMgQwe" +
                "lXcuoT4HAD/8McBJtmQWQRsBCITlha9mphw6NYjf/Ysm1hIBHUdbLm6b90LaGBDvQvrK6EW2Cbs3lwKelgTK59+dc6lH7vH+nteeY/lGxT9EUBot8c/Q1BUE4kvTrdHUeKFHs81yzAY" +
                "azVVitPGvNnnUbiwa68zur9El2/+qBLA+51wuPIkxR+wURgPnDdkp7/bqCbzISB8BFDuHD2ulInZZhGcWEae6vcReUbo5O8/UYVNlz15KEVcSgBJ7Qq1Q7+QsctgqcAF8+HGhx5Nabe" +
                "Hzbkc]]></Encrypt><AgentID><![CDATA[]]></AgentID></xml>";
        String echostr = "";  //接口验证， 会带有
//        String result = qyweixinGatewayInnerService.recvMsgEvent(msgSignature, timeStamp, nonce, postData, echostr, appId);
//        log.info("trace recvMsgEventSaveSuiteToken result:{}", result);
    }


    //ceshi113
    String appId = "wx4c7edab730f4fdc9";
    @Test
    public void getSuiteAccessToken() {
        String result = qyweixinGatewayInnerService.getSuiteAccessToken(appId);
        log.info("trace getSuiteAccessToken result:{}", result);
    }

    @Test
    public void getPreAuthCode() {
        String result = qyweixinGatewayInnerService.getPreAuthCode(appId);
        log.info("trace getPreAuthCode result:{}", result);
    }

    String caller="ChenAFe";
    String authCorpId="wwedffbd44d73612f4";
    @Test
    public void getDial() {
        Result<String> result = qyweixinGatewayInnerService.doDial(caller, authCorpId);
        log.info("trace getDial result:{}", result);
    }



//    @Test
//    public void installAuth() {
//        String authCode= "6Uo-tjjbVyvnAEiolqnuOv3v0hqICxBiaHsnuvC7tCpjNuNK9w1Pi0AKrbbU2E6d1Xyw3NWDMNfW-VidC7PWZo0dpCE38lTV8FMzqiTSb-U";
//        Result<String> result = qyweixinGatewayInnerService.getAuthInfoDoInitCorp(authCode, "wx88a141937dd6f838", null);
//        log.info("trace installAuth result:{}", result);
//    }


    @Test
    public void getRegisterCode() {
        String result = qyweixinGatewayInnerService.getRegisterCode("3414311");
        log.info("trace getRegisterCode result:{}", result);
    }

    @Test
    public void loginAuth() {
        Object result = qyweixinGatewayInnerService.loginAuth("1", appId,null);
        log.info("trace loginAuth result:{}", result);
    }

    @Test
    public void appAuth() {
        String code = "0IlpVXYuko1vnqUsLUQaqs346Q9A7g09vZMXG3T7KUc";
        Result<CorpTicketResult> result = qyweixinGatewayInnerService.appAuth(code, appId,null);
        log.info("trace appAuth result:{}", result);
    }

//    @Test
//    public void testMessage() {
//        String result = qyweixinGatewayInnerService.sendMessageTest();
//        log.info("trace appAuth result:{}", result);
//    }

    String fsEa = "58463";
    @Test
    public void testUpdateCorpInfo() {

    }

    @Test
    public void payForAppSuccessEvent() {
        qyweixinGatewayInnerService.payForAppSuccessEvent("wx88a141937dd6f838","T00000C661DC35F5B2A74D361C4C4");
    }

//    @Test
//    public void deleteDepartment() {
//        qyweixinGatewayInnerService.deleteDepartment("82235","ww214828df2e047e24","wx88a141937dd6f838","2");
//    }


//    @Test
//    public void testCorpName() {
//        qyweixinGatewayInnerService.saveCorpInfo(null, "ww7e3b4b1681c29d47", "112测试");
//    }
//
//    @Test
//    public void testCorpId() {
//        qyweixinGatewayInnerService.saveCorpInfo("test", "wwf1b3925aa217d4b2", "test");
//    }

    @Test
    public void deleteExternalMigration() {
        //String xml = "<xml><SuiteId><![CDATA[wx88a141937dd6f838]]></SuiteId><AuthCorpId><![CDATA[wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA]]></AuthCorpId><InfoType><![CDATA[change_external_contact]]></InfoType><TimeStamp>1676968653</TimeStamp><ChangeType><![CDATA[edit_external_contact]]></ChangeType><UserID><![CDATA[wowx1mDAAArLI4aPJXTqJx_UJCABHILQ]]></UserID><ExternalUserID><![CDATA[wmwx1mDAAAEHC7C10PhMXCGverrKMlKQ]]></ExternalUserID></xml>";
        //String xml = "<xml><SuiteId><![CDATA[wx88a141937dd6f838]]></SuiteId><AuthCorpId><![CDATA[wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA]]></AuthCorpId><InfoType><![CDATA[change_external_contact]]></InfoType><TimeStamp>1677031161</TimeStamp><ChangeType><![CDATA[del_external_contact]]></ChangeType><UserID><![CDATA[wowx1mDAAArLI4aPJXTqJx_UJCABHILQ]]></UserID><ExternalUserID><![CDATA[wmwx1mDAAAT5X0BAeycZzIzm1215otGA]]></ExternalUserID></xml>";
        //String xml = "<xml><SuiteId><![CDATA[wxdeb7e0658a828754]]></SuiteId><AuthCorpId><![CDATA[wpwx1mDAAAiOQQUxgw2gFuEGkdG6_kkA]]></AuthCorpId><InfoType><![CDATA[change_external_chat]]></InfoType><TimeStamp>1694079619</TimeStamp><ChatId><![CDATA[wrwx1mDAAAeYJ4gI8MOASuxD2JLY5cMQ]]></ChatId><ChangeType><![CDATA[update]]></ChangeType><UpdateDetail><![CDATA[change_name]]></UpdateDetail></xml>";
        String xml = "<xml><SuiteId><![CDATA[wx88a141937dd6f838]]></SuiteId><AuthCorpId><![CDATA[wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA]]></AuthCorpId><InfoType><![CDATA[change_external_contact]]></InfoType><TimeStamp>1715570686</TimeStamp><ChangeType><![CDATA[edit_external_contact]]></ChangeType><UserID><![CDATA[wowx1mDAAArLI4aPJXTqJx_UJCABHILQ]]></UserID><ExternalUserID><![CDATA[wmwx1mDAAAEHC7C10PhMXCGverrKMlKQ]]></ExternalUserID></xml>";
        qyweixinGatewayInnerService.recvMsgEvent(xml,"");
    }
//    @Test
//    public void testCorpIdha() {
//        qyweixinGatewayInnerService.getAccessTokenInfo("84883", "wx88a141937dd6f838");
//    }

    @Test
    public void changeExternalContact() {
        String xml = "<xml><SuiteId><![CDATA[wx88a141937dd6f838]]></SuiteId><AuthCorpId><![CDATA[wpwx1mDAAAcy_L-ekn3ZBDU8-3CCvIjw]]></AuthCorpId><InfoType><![CDATA[change_external_contact]]></InfoType><TimeStamp>1650270875</TimeStamp><ChangeType><![CDATA[add_external_contact]]></ChangeType><UserID><![CDATA[wowx1mDAAAIPKnnZb_x7bTpzzlYrJJug]]></UserID><ExternalUserID><![CDATA[wowx1mDAAAZsPfjUCiEB9hdEfjw-82UA]]></ExternalUserID><WelcomeCode><![CDATA[Sap2OKXtENZV9HcAY7FNa-q0bw7mtorsT_IBsfjQ4QE]]></WelcomeCode></xml>";
        //ExternalContactEventXml externalContactEventXml = XmlParser.fromXml(xml, ExternalContactEventXml.class);
        ExternalContactEventXml externalContactEventXml = XStreamUtils.parseXml(xml, ExternalContactEventXml.class);
        //qyweixinGatewayInnerService.changeExternalContact(externalContactEventXml);
    }

    @Test
    public void testGetGroupChat() {
        QyweixinGroupChatInfo groupChatInfo = new QyweixinGroupChatInfo();
        groupChatInfo.setEa("84883");
        Result<QyweixinGroupChatResult> result = qyweixinGatewayInnerService.getGroupChat(groupChatInfo);
        System.out.println(result);
    }

    @Test
    public void getGroupChatDetail() {
        Result<QyweixinGroupChatDetail> result = qyweixinGatewayInnerService.getGroupChatDetail("84883",
                "wrwx1mDAAA9mGACYP64NamS0r7QmiDCQ","wx88a141937dd6f838");
        System.out.println(result);
    }

    @Test
    public void getGroupChatList() {
        QyweixinGroupChatInfo groupChatInfo = new QyweixinGroupChatInfo();
        groupChatInfo.setEa("84883");
        groupChatInfo.setLimit(1000);
        groupChatInfo.setUserId("wowx1mDAAArLI4aPJXTqJx_UJCABHILQ");
        Result<GroupChatListResult> result = qyweixinGatewayInnerService.getGroupChatList(groupChatInfo);
        System.out.println(result);
    }

    @Test
    public void getPermitUserList2() {
        Result<GetPermitUserListResult> result = qyweixinGatewayInnerService.getPermitUserList2("84883",null);
        System.out.println(result);
    }

    @Test
    public void checkRoomAgree() {
        Result<CheckRoomAgreeResult> result = qyweixinGatewayInnerService.checkRoomAgree2("84883","wrQZ1uJQAAEH3PMSeQ96V8cqvhy7WQKg",null);
        System.out.println(result);
    }

    @Test
    public void checkSingleAgree() {
        QyweixinCheckSingleAgreeArg arg = new QyweixinCheckSingleAgreeArg();
        arg.setFsEa("84883");
        QyweixinCheckSingleAgreeArg.Info info = new QyweixinCheckSingleAgreeArg.Info();
        info.setUserid("chenzongxin");
        info.setExteranalopenid("wmQZ1uJQAAP76E4294nLs3QgHvThowFA");
        List<QyweixinCheckSingleAgreeArg.Info> infoList = new LinkedList<>();
        infoList.add(info);
        arg.setInfo(infoList);
        Result<CheckSingleAgreeResult> result = qyweixinGatewayInnerService.checkSingleAgree(arg);
        System.out.println(result);
    }

    @Test
    public void testXStream() {
        String xml = "<xml><SuiteId><![CDATA[wx88a141937dd6f838]]></SuiteId><AuthCorpId><![CDATA[wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA]]></AuthCorpId><InfoType><![CDATA[陈辰]]></InfoType><TimeStamp>1677031161</TimeStamp><ChangeType><![CDATA[del_external_contact]]></ChangeType><UserID><![CDATA[wowx1mDAAArLI4aPJXTqJx_UJCABHILQ]]></UserID><ExternalUserID><![CDATA[wmwx1mDAAAT5X0BAeycZzIzm1215otGA]]></ExternalUserID></xml>";
        try {
            // 创建文件写入器
            FileWriter fileWriter = new FileWriter("D:\\fxiaoke\\WXWork\\xml\\xStream\\xStream5.txt", true);

            // 创建缓冲写入器
            BufferedWriter bufferedWriter = new BufferedWriter(fileWriter);

            // 清空缓存
            XStreamUtils.cleanXStreamAllData();

            for (int i = 0; i < 3000; i++) {
                int finalI = i;
                int finalI1 = i;
                ThreadPoolHelper.msgEventThreadPool.submit(()->{
                    Thread thread = Thread.currentThread();
                    // 获取线程的名称
                    String threadName = thread.getName();
                    try {
                        //第一次解析xml
                        Long startTime = 0L;
                        Long endTime = 0L;
                        if(finalI1 % 2 == 0) {
                            startTime = System.currentTimeMillis();
                            SuiteAuthXml baseMsgXml = XStreamUtils.parseXml(xml, SuiteAuthXml.class);
                            endTime = System.currentTimeMillis();
                        } else if(finalI1 % 3 == 0) {
                            startTime = System.currentTimeMillis();
                            RepChangeContactXml baseMsgXml = XStreamUtils.parseXml(xml, RepChangeContactXml.class);
                            endTime = System.currentTimeMillis();
                        } else {
                            startTime = System.currentTimeMillis();
                            QyweixinMsgBaseXml baseMsgXml = XStreamUtils.parseXml(xml, QyweixinMsgBaseXml.class);
                            endTime = System.currentTimeMillis();
                        }
                        bufferedWriter.write("第"+ (finalI + 1) +"次轮询"+ threadName +"第一次解析xml,time="+ (endTime - startTime) + "ms");
                        bufferedWriter.newLine(); // 写入换行符
                        bufferedWriter.flush();
                        //第二次解析xml
                        Long startTime1 = 0L;
                        Long endTime1 = 0L;
                        if(finalI1 % 2 == 0) {
                            startTime1 = System.currentTimeMillis();
                            SuiteAuthXml baseMsgXml = XStreamUtils.parseXml(xml, SuiteAuthXml.class);
                            endTime1 = System.currentTimeMillis();
                        } else if(finalI1 % 3 == 0) {
                            startTime1 = System.currentTimeMillis();
                            RepChangeContactXml baseMsgXml = XStreamUtils.parseXml(xml, RepChangeContactXml.class);
                            endTime1 = System.currentTimeMillis();
                        } else {
                            startTime1 = System.currentTimeMillis();
                            QyweixinMsgBaseXml baseMsgXml = XStreamUtils.parseXml(xml, QyweixinMsgBaseXml.class);
                            endTime1 = System.currentTimeMillis();
                        }
                        bufferedWriter.write("第"+ (finalI + 1) +"次轮询"+ threadName +"第二次解析xml,time="+ (endTime1 - startTime1) + "ms");
                        bufferedWriter.newLine(); // 写入换行符
                        bufferedWriter.flush();
                        //第一次解析xml
                        Long startTime2 = 0L;
                        Long endTime2 = 0L;
                        if(finalI1 % 2 == 0) {
                            startTime2 = System.currentTimeMillis();
                            SuiteAuthXml baseMsgXml = XStreamUtils.parseXml(xml, SuiteAuthXml.class);
                            endTime2 = System.currentTimeMillis();
                        } else if(finalI1 % 3 == 0) {
                            startTime2 = System.currentTimeMillis();
                            RepChangeContactXml baseMsgXml = XStreamUtils.parseXml(xml, RepChangeContactXml.class);
                            endTime2 = System.currentTimeMillis();
                        } else {
                            startTime2 = System.currentTimeMillis();
                            QyweixinMsgBaseXml baseMsgXml = XStreamUtils.parseXml(xml, QyweixinMsgBaseXml.class);
                            endTime2 = System.currentTimeMillis();
                        }
                        bufferedWriter.write("第"+ (finalI + 1) +"次轮询"+ threadName +"第三次解析xml,time="+ (endTime2 - startTime2) + "ms");
                        bufferedWriter.newLine(); // 写入换行符
                        bufferedWriter.flush();
                        //第一次解析xml
                        Long startTime3 = 0L;
                        Long endTime3 = 0L;
                        if(finalI1 % 2 == 0) {
                            startTime3 = System.currentTimeMillis();
                            SuiteAuthXml baseMsgXml = XStreamUtils.parseXml(xml, SuiteAuthXml.class);
                            endTime3 = System.currentTimeMillis();
                        } else if(finalI1 % 3 == 0) {
                            startTime3 = System.currentTimeMillis();
                            RepChangeContactXml baseMsgXml = XStreamUtils.parseXml(xml, RepChangeContactXml.class);
                            endTime3 = System.currentTimeMillis();
                        } else {
                            startTime3 = System.currentTimeMillis();
                            QyweixinMsgBaseXml baseMsgXml = XStreamUtils.parseXml(xml, QyweixinMsgBaseXml.class);
                            endTime3 = System.currentTimeMillis();
                        }
                        bufferedWriter.write("第"+ (finalI + 1) +"次轮询"+ threadName +"第四次解析xml,time="+ (endTime3 - startTime3) + "ms");
                        bufferedWriter.newLine(); // 写入换行符
                        bufferedWriter.flush();
                        //第一次解析xml
                        Long startTime4 = 0L;
                        Long endTime4 = 0L;
                        if(finalI1 % 2 == 0) {
                            startTime4 = System.currentTimeMillis();
                            SuiteAuthXml baseMsgXml = XStreamUtils.parseXml(xml, SuiteAuthXml.class);
                            endTime4 = System.currentTimeMillis();
                        } else if(finalI1 % 3 == 0) {
                            startTime4 = System.currentTimeMillis();
                            RepChangeContactXml baseMsgXml = XStreamUtils.parseXml(xml, RepChangeContactXml.class);
                            endTime4 = System.currentTimeMillis();
                        } else {
                            startTime4 = System.currentTimeMillis();
                            QyweixinMsgBaseXml baseMsgXml = XStreamUtils.parseXml(xml, QyweixinMsgBaseXml.class);
                            endTime4 = System.currentTimeMillis();
                        }
                        bufferedWriter.write("第"+ (finalI + 1) +"次轮询"+ threadName +"第五次解析xml,time="+ (endTime4 - startTime4) + "ms");
                        bufferedWriter.newLine(); // 写入换行符
                        bufferedWriter.flush();
                    } catch (Exception e) {
                        log.info("QyweixinGatewayInnerServiceImpl.onMsgEvent,exception={}", e.getMessage(),e);
                    }
                });
            }
            Thread.sleep(60000); // 等待1分钟
            ConcurrentHashMap map = XStreamUtils.getXStreamAllData();
            bufferedWriter.write(map.toString());
            bufferedWriter.newLine(); // 写入换行符
            bufferedWriter.flush();
        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void onRepEvent() {
        EnterpriseWeChatEventProto eventProto = new EnterpriseWeChatEventProto();
        eventProto.setAppId(null);
        eventProto.setSignature("71105857b3007bf0d52d4a692b71d7fc6a8d9fc6");
        eventProto.setNonce("1704054881");
        eventProto.setEchoStr(null);
        eventProto.setData("<xml><ToUserName><![CDATA[dk34d6bacd61e33d99]]></ToUserName><Encrypt><![CDATA[+uHk9tRjgGLneFAWjBeiQs+AixkRd6CmsThhn2S/eYQnyyJIBHTfzEEvQp0fMqJnvfdT1cdTSMFT1yn5cwlUOa820fMCNEKou7PB7UqVR23mCekcy0yS2hGgGB7CIF5mGY2tsEucxegEHSnmWTMzcGuItTCulzOFNLnV8dOGz80buTrXeuwPF0qvrj1heGchtjGrzONCJ7Mg7C3ABsySRAUZxr6e0+dvy+K41D/x0gprCl8Wx8SkxN/wzMdG6+ETrHVcptSXHZ/t/XpgyFa0YrM+9p4uTIgkwVLzVoGBJUZB3TNrhfWzE4jX56X2ZwT4e0UsVYckoLB0/tOLzra9JCKBLzgE+IQlOHhkkdY88CQGSdtZyb5c8REZ5pHCQBxV]]></Encrypt><AgentID><![CDATA[]]></AgentID></xml>");
        eventProto.setTimestamp("1704938111");

        String onRepEvent = qyweixinGatewayInnerService.onRepEvent(eventProto);
        System.out.println(onRepEvent);
    }

    @Test
    public void decryptStr() {
        String msg = "7CaTSytLuepalVwZoMoWUluLa2fd5Y9QM-yaAALE6S8";
        String msg2 = SecurityUtil.encryptStr(msg);
        System.out.println(msg2);
    }

    @Test
    public void resetPermanentCode() {
//        qyweixinGatewayInnerService.resetPermanentCode("dk34d6bacd61e33d99",
//                "7NoExJiF0pwFs6gvjDZmpTdak7i08jCasBnzCFR7TlY9M-joMVnbdfwnzYeHdjuQJzfh1PDSXiTQJ0KOuxtX7rcNodAOBqb0MaPiKa2Pe7Y");
    }

    @Test
    public void getUserInfoByPhone() {
        com.facishare.open.qywx.accountinner.result.Result<QyweixinUserDetailInfoRsp> result = qyweixinGatewayInnerService.getUserInfoByPhone("wpwx1mDAAAsJuqgPFlcfGWtLOewfEiNw",
                "dk34d6bacd61e33d99",
                "***********");
        System.out.println(result);
    }

    @Test
    public void onEnterAgent() {
//        EventXml eventXml = new EventXml();
//        eventXml.setToUserName("wpwx1mDAAAFtBrQCfiHtmRaUHt5hRo7A");
//        eventXml.setFromUserName("wowx1mDAAA4ZIEKKwkyK-R8sc0g-yS0w");
//        eventXml.setEvent("enter_agent");
//        eventXml.setAgentID("1000003");
//        qyweixinGatewayInnerService.onEnterAgent(eventXml);
    }

    @Test
    public void parseXml() {
        String plainMsg = "<xml><ToUserName><![CDATA[wpwx1mDAAAFtBrQCfiHtmRaUHt5hRo7A]]></ToUserName><FromUserName><![CDATA[wowx1mDAAA4ZIEKKwkyK-R8sc0g-yS0w]]></FromUserName><CreateTime>**********</CreateTime><MsgType><![CDATA[event]]></MsgType><AgentID>1000003</AgentID><Event><![CDATA[enter_agent]]></Event><EventKey><![CDATA[]]></EventKey></xml>";
        EventXml eventXml = XStreamUtils.parseXml(plainMsg, EventXml.class);
        System.out.println(eventXml);
    }

    @Test
    public void queryConnectInfo() {
        Result<QYWXConnectParam> result = qyweixinGatewayInnerService.queryConnectInfo("89306", "6683c56b350d680001cd184f");
        System.out.println(result);
    }

    @Test
    public void fsBindWithQywx() {
        QYWXConnectParam connectParam = new QYWXConnectParam();
        connectParam.setDataCenterId("678f3d12fa12272848ea36981");
        connectParam.setFsEa("82777");
        connectParam.setAppId("dk2819cf1b932d7406");
        connectParam.setDataCenterName("贝贝CRM断网测试企业1");
        connectParam.setOutEa("wpwx1mDAAApDuHDla66rUas9XUWu0rdg");
        connectParam.setOutDepId("1");
        Result<Void> result = qyweixinGatewayInnerService.fsBindWithQywx(connectParam);
        System.out.println(result);
    }

    @Test
    public void fsUnBindWithQywx() {
        QYWXConnectParam connectParam = new QYWXConnectParam();
        connectParam.setFsEa("81243");
        connectParam.setDataCenterId("66d184ff24944600017fda672");
        connectParam.setDataCenterName("企微上游");
        connectParam.setOutEa("wpwx1mDAAACigpu1Ro4u89tCMaZns7ag");
        Result<Void> result = qyweixinGatewayInnerService.fsUnBindWithQywx(connectParam);
        System.out.println(result);
        //fsBindWithQywx();
    }

    @Test
    public void checkAndInitConnector() {
        Result<Void> result = qyweixinGatewayInnerService.checkAndInitConnector("90896","");
        System.out.println(result);
        //fsBindWithQywx();
    }

    @Test
    public void onShareChainChange() {
//        EventXml eventXml = new EventXml();
//        eventXml.setToUserName("wpwx1mDAAACigpu1Ro4u89tCMaZns7ag");
//        eventXml.setEvent("share_chain_change");
//        eventXml.setMsgType("event");
//        eventXml.setAgentID("1000002");
//        qyweixinGatewayInnerService.onShareChainChange(eventXml);
//        System.out.println("ok");
    }

    @Test
    public void recvMsgEventT() {
        String s = qyweixinGatewayInnerService.recvMsgEvent("<xml><SuiteId><![CDATA[wx105357ca56c6db18]]></SuiteId><InfoType><![CDATA[suite_ticket]]></InfoType><TimeStamp>1715591653</TimeStamp><SuiteTicket><![CDATA[dMWu7oGr1oCudahbdefOG7TYYYg1H33r8HRitpNtvemj-UeWWTbKpPKYwPfMqPYD]]></SuiteTicket></xml>",null);
        System.out.println(s);
    }

//    @Test
//    public void getMainAppId() {
//        String s = qyweixinGatewayInnerService.getMainAppId("wpwx1mDAAAEO7X_Roe5s3viyg0z_c06w").getData();
//        System.out.println(s);
//    }
    @Test
    public void getDownStreamUserDetailInfo(){
        Result<DownStreamUserDetailInfo> result=qyweixinGatewayInnerService.getDownStreamUserDetailInfo("",
                "","","");
        System.out.println(result);
    }

}
